// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:legacy/controllers/network_controller.dart';
// import 'package:loader_overlay/loader_overlay.dart';

// import '../../common_widgets/videoplayer/videoplayer.dart';
// import '../../utils/app_pref.dart';
// import '../../utils/app_styles.dart';
// import '../../utils/appcolors.dart';
// import '../../utils/gaps.dart';
// import '../../utils/images.dart';
// import '../../utils/progress.dart';
// import '../../utils/search.dart';
// import 'cubit/cubit/channellist_cubit.dart';
// import 'models/channellistmodel.dart';

// class ChannelsList extends StatefulWidget {
//   const ChannelsList({super.key, this.category, this.name});

//   final category;
//   final name;

//   @override
//   State<ChannelsList> createState() => _channelsState();
// }

// class _channelsState extends State<ChannelsList> {
//   List<Datum> channellistvideos = [];
//   String searchQuery = '';
//   // List<String> data = ['Apple', 'Banana', 'Orange', 'Mango', 'Pineapple'];
//   // List <String> data=[];
// List<Datum> get filteredData {
//     return channellistvideos
//         .where((item) => item.toLowerCase().contains(searchQuery.toLowerCase()))
//         .toList();
//   }

//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     context.read<ChannellistCubit>().channellistvideoLoad(widget.category);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: Scaffold(
//           body: AppLoader(
//               child: BlocConsumer<ChannellistCubit, ChannellistState>(
//                   listener: (context, state) {
//         if (state is Loaded) {
//           print('55555555${state.channellistdetails!.data}');

//           setState(() {
//             channellistvideos = state.channellistdetails?.data as List<Datum>;
//           });

//         }
//         // TODO: implement listener
//       }, builder: (context, state) {

//         (state is Loading)
//             ? context.loaderOverlay.show()
//             : context.loaderOverlay.hide();
//         return Column(
//           children: [
//             Container(
//               height: 150,
//               width: double.infinity,
//               decoration: BoxDecoration(
//                   // color: Colors.red,
//                   image: DecorationImage(
//                       colorFilter: ColorFilter.mode(
//                           Colors.black.withOpacity(0.5), BlendMode.darken),
//                       image: const AssetImage('assets/images/tile_top_bg.png'),
//                       fit: BoxFit.cover)),
//               child: Column(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     Row(
//                       children: [
//                         Padding(
//                           padding: const EdgeInsets.all(8.0),
//                           child: GestureDetector(
//                             onTap: () => Navigator.pop(context),
//                             child: SizedBox(
//                                 height: 20,
//                                 child:
//                                     Image.asset('assets/images/backArrow.png')),
//                           ),
//                         ),
//                         Padding(
//                           padding: const EdgeInsets.only(left: 20),
//                           child: Text(widget.name, style: textstyleheading),
//                         )
//                       ],
//                     ),
//                     gapH16,
//                     Padding(
//                       padding: const EdgeInsets.symmetric(horizontal: 15),
//                       child: Container(
//                         decoration: BoxDecoration(
//                             color: Colors.black.withOpacity(.3),
//                             borderRadius: BorderRadius.circular(8)),
//                         height: 45,
//                         child: Center(
//                           child: TextFormField(
//                             // validator: validateEmail,
//                             // controller: emailcontroller,
// //                             onChanged: (value){
// //                               setState(() {
// // //  channellistvideos=channellistvideos[0].title.rem
// //                               });
// //                             },
//                             onChanged: (value) {
//                               setState(() {
//                                 searchQuery = value;
//                               });
//                             },
//                             decoration: InputDecoration(
//                                 hintStyle: TextStyle(
//                                     color: Colors.white.withOpacity(.5),
//                                     fontSize: 15),
//                                 prefixIcon: Padding(
//                                   padding: const EdgeInsets.all(15.0),
//                                   child: Image.asset(
//                                     'assets/images/SearchIcon.png',
//                                     height: 20,
//                                     width: 10,
//                                   ),
//                                 ),
//                                 focusedBorder: OutlineInputBorder(
//                                     borderSide: BorderSide(
//                                         color: AppColors.colorPrimary),
//                                     borderRadius: BorderRadius.circular(8)),
//                                 border: OutlineInputBorder(
//                                     borderRadius: BorderRadius.circular(8)),
//                                 // labelText: "Search Audios",
//                                 // labelStyle: TextStyle(color: AppColors.colorPrimary),
//                                 hintText: "Search Videos"),
//                           ),
//                         ),
//                       ),
//                     ),
//                   ]),
//             ),
//             Expanded(
//               child: Padding(
//                 padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 5),
//                 child: SizedBox(
//                   // height: MediaQuery.of(context).size.height,
//                   child: GridView.builder(
//                     itemCount: filteredData.length,
//                     gridDelegate:
//                         const SliverGridDelegateWithFixedCrossAxisCount(
//                             childAspectRatio: 3.12 / 2.2,
//                             mainAxisSpacing: 5,
//                             crossAxisSpacing: 5,
//                             crossAxisCount: 2),
//                     itemBuilder: (BuildContext context, int index) {
//                       return GestureDetector(
//                           onTap: () {
//                             AppPref.setString('vimeoId',
//                                 channellistvideos[index].vimeoId ?? 'j');

//                             // String? token1 = AppPref.getString('token');
//                             Navigator.of(context).push(MaterialPageRoute(
//                                 builder: (BuildContext context) =>
//                                     const VideoPlayerPage()));
//                           },
//                           child: Container(
//                             decoration: BoxDecoration(
//                                 borderRadius: BorderRadius.circular(4),
//                                 image: DecorationImage(
//                                     colorFilter: ColorFilter.mode(
//                                         Colors.black.withOpacity(0.3),
//                                         BlendMode.darken),
//                                     image: CachedNetworkImageProvider(
//                                         channellistvideos[index]
//                                                 .thumbnail
//                                                 .toString() ??
//                                             'https://i.etsystatic.com/22794840/r/il/d3d2cf/**********/il_794xN.**********_j3ov.jpg'),
//                                     fit: BoxFit.cover)),
//                             child: Column(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 SizedBox(
//                                   height: 50,
//                                   width: 50,
//                                   child: Image.asset(
//                                     Images.playIcon,
//                                   ),
//                                 ),
//                                 // Text(filteredData[index]),
//                                 Padding(
//                                     padding: const EdgeInsets.only(bottom: 4),
//                                     child: Text(
//                                         channellistvideos[index]
//                                             .title
//                                             .toString()
//                                             .toUpperCase(),
//                                         style: textstyle1))
//                               ],
//                             ),
//                           ));
//                     },
//                   ),
//                 ),
//               ),
//             )
//           ],
//         );
//       }))),
//     );
//   }
// }
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import '../../common_widgets/videoplayer/videoplayer.dart';
import '../../utils/app_pref.dart';
import '../../utils/app_styles.dart';
import '../../utils/appcolors.dart';
import '../../utils/gaps.dart';
import '../../utils/images.dart';
import '../../utils/progress.dart';
import 'cubit/cubit/channellist_cubit.dart';
import 'models/channellistmodel.dart';

class ChannelsList extends StatefulWidget {
  const ChannelsList({super.key, this.category, this.name});

  final category;
  final name;

  @override
  State<ChannelsList> createState() => _channelsState();
}

class _channelsState extends State<ChannelsList> {
  List<Datum> channelListFull = [];
  List<Datum> channelListFiltered = [];

  var controller = TextEditingController();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<ChannellistCubit>().channellistvideoLoad(widget.category);
  }

  void resetSearch() {
    setState(() {
      channelListFiltered.clear();
      channelListFiltered = channelListFull;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: AppLoader(
          child: BlocConsumer<ChannellistCubit, ChannellistState>(
            listener: (context, state) {
              if (state is Loaded) {
                print('55555555${state.channellistdetails!.data}');

                setState(() {
                  channelListFull =
                      state.channellistdetails?.data as List<Datum>;
                  channelListFiltered = channelListFull;
                });
              }
              // TODO: implement listener
            },
            builder: (context, state) {
              (state is Loading)
                  ? context.loaderOverlay.show()
                  : context.loaderOverlay.hide();
              return Column(
                children: [
                  Container(
                    height: 150,
                    width: double.infinity,
                    decoration: BoxDecoration(
                        // color: Colors.red,
                        image: DecorationImage(
                            colorFilter: ColorFilter.mode(
                                Colors.black.withOpacity(0.5),
                                BlendMode.darken),
                            image: const AssetImage(
                                'assets/images/tile_top_bg.png'),
                            fit: BoxFit.cover)),
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: GestureDetector(
                                  onTap: () => Navigator.pop(context),
                                  child: SizedBox(
                                      height: 20,
                                      child: Image.asset(
                                          'assets/images/backArrow.png')),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20),
                                child:
                                    Text(widget.name, style: textstyleheading),
                              )
                            ],
                          ),
                          gapH16,
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 15),
                            child: Container(
                              decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(.3),
                                  borderRadius: BorderRadius.circular(8)),
                              height: 45,
                              child: Center(
                                child: TextFormField(
                                  style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold),
                                  // validator: validateEmail,
                                  controller: controller,
                                  onChanged: (value) {
                                    print(
                                        '--------------------------on changed------------------------------');
                                    List<Datum> tempList =
                                        List.empty(growable: true);
                                    setState(() {
                                      if (controller.value.text.isEmpty) {
                                        resetSearch();
                                      }
                                      for (var i in channelListFull) {
                                        if (i.title!
                                            .toLowerCase()
                                            .contains(controller.value.text)) {
                                          tempList.add(i);
                                        }
                                      }
                                      // channelListFiltered.clear();
                                      channelListFiltered = tempList;
                                    });
                                  },
                                  decoration: InputDecoration(
                                      hintStyle: TextStyle(
                                          color: Colors.white.withOpacity(.5),
                                          fontSize: 15),
                                      prefixIcon: Padding(
                                        padding: const EdgeInsets.all(15.0),
                                        child: Image.asset(
                                          'assets/images/SearchIcon.png',
                                          height: 20,
                                          width: 10,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                              color: AppColors.colorPrimary),
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                      border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                      // labelText: "Search Audios",
                                      // labelStyle: TextStyle(color: AppColors.colorPrimary),
                                      hintText: "Search Videos"),
                                ),
                              ),
                            ),
                          ),
                        ]),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 2, vertical: 5),
                      child: SizedBox(
                        // height: MediaQuery.of(context).size.height,
                        child: GridView.builder(
                          itemCount: channelListFiltered.length,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                                  childAspectRatio: 3.12 / 2.2,
                                  mainAxisSpacing: 5,
                                  crossAxisSpacing: 5,
                                  crossAxisCount: 2),
                          itemBuilder: (BuildContext context, int index) {
                            return GestureDetector(
                                onTap: () {
                                  AppPref.setString(
                                      'vimeoId',
                                      channelListFiltered[index].vimeoId ??
                                          'j');

                                  // String? token1 = AppPref.getString('token');
                                  pushNewScreen(
                                    context,
                                    screen: VideoPlayerPage(),
                                    withNavBar:
                                        false, // OPTIONAL VALUE. True by default.
                                    pageTransitionAnimation:
                                        PageTransitionAnimation.cupertino,
                                  );
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(4),
                                      image: DecorationImage(
                                          colorFilter: ColorFilter.mode(
                                              Colors.black.withOpacity(0.3),
                                              BlendMode.darken),
                                          image: CachedNetworkImageProvider(
                                              channelListFiltered[index]
                                                      .thumbnail
                                                      .toString() ??
                                                  'https://i.etsystatic.com/22794840/r/il/d3d2cf/**********/il_794xN.**********_j3ov.jpg'),
                                          fit: BoxFit.cover)),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        height: 50,
                                        width: 50,
                                        child: Image.asset(
                                          Images.playIcon,
                                        ),
                                      ),
                                      Padding(
                                          padding:
                                              const EdgeInsets.only(bottom: 4),
                                          child: Text(
                                              channelListFiltered[index]
                                                  .title
                                                  .toString()
                                                  .toUpperCase(),
                                              style: textstyle1))
                                    ],
                                  ),
                                ));
                          },
                        ),
                      ),
                    ),
                  )
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
