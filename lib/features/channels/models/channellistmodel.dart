// To parse this JSON data, do
//
//     final channelListVideoModel = channelListVideoModelFromJson(jsonString);

import 'dart:convert';

ChannelListVideoModel channelListVideoModelFromJson(String str) =>
    ChannelListVideoModel.fromJson(json.decode(str));

String channelListVideoModelToJson(ChannelListVideoModel data) =>
    json.encode(data.toJson());

class ChannelListVideoModel {
  bool? status;
  String? message;
  List<Datum>? data;

  ChannelListVideoModel({
    this.status,
    this.message,
    this.data,
  });

  factory ChannelListVideoModel.fromJson(Map<String, dynamic> json) =>
      ChannelListVideoModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  String? id;
  String? churchId;
  String? title;
  String? description;
  String? author;
  String? duration;
  String? thumbnail;
  String? type;
  String? media;
  String? channelId;
  String? vimeoId;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;

  Datum({
    this.id,
    this.churchId,
    this.title,
    this.description,
    this.author,
    this.duration,
    this.thumbnail,
    this.type,
    this.media,
    this.channelId,
    this.vimeoId,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["_id"],
        churchId: json["churchId"],
        title: json["title"],
        description: json["description"],
        author: json["author"],
        duration: json["duration"],
        thumbnail: json["thumbnail"],
        type: json["type"],
        media: json["media"],
        channelId: json["channelId"],
        vimeoId: json["vimeoId"],
        status: json["status"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "churchId": churchId,
        "title": title,
        "description": description,
        "author": author,
        "duration": duration,
        "thumbnail": thumbnail,
        "type": type,
        "media": media,
        "channelId": channelId,
        "vimeoId": vimeoId,
        "status": status,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };

  toLowerCase() {}
}
