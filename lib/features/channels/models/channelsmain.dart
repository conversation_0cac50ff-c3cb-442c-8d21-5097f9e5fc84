import 'dart:convert';

ChannelVideoModel channelVideoModelFromJson(String str) =>
    ChannelVideoModel.fromJson(json.decode(str));

String channelVideoModelToJson(ChannelVideoModel data) =>
    json.encode(data.toJson());

class ChannelVideoModel {
  ChannelVideoModel({
    this.status,
    this.message,
    this.data,
  });

  bool? status;
  String? message;
  List<Datum>? data;

  factory ChannelVideoModel.fromJson(Map<String, dynamic> json) =>
      ChannelVideoModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  Datum({
    this.id,
    this.name,
    this.churchId,
    this.thumbnail,
    this.type,
    this.createdAt,
  });

  String? id;
  String? name;
  String? churchId;
  String? thumbnail;
  String? type;
  DateTime? createdAt;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["_id"],
        name: json["name"],
        churchId: json["churchId"],
        thumbnail: json["thumbnail"],
        type: json["type"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "churchId": churchId,
        "thumbnail": thumbnail,
        "type": type,
        "createdAt": createdAt?.toIso8601String(),
      };
}
