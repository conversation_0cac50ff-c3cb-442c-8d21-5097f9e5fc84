import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/common_widgets/sidedrawer/side_drawer.dart';
import 'package:legacy/features/channels/channellist.dart';
import 'package:legacy/features/channels/cubit/channel_cubit.dart';
import 'package:legacy/features/channels/models/channelsmain.dart';
import 'package:legacy/utils/app_styles.dart';
import 'package:legacy/utils/progress.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import '../../main.dart';
import '../../utils/app_pref.dart';
import '../../utils/images.dart';

class channels extends StatefulWidget {
  const channels({super.key});

  @override
  State<channels> createState() => _channelsState();
}

class _channelsState extends State<channels> {
  List<Datum> channelvideos = [];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<ChannelCubit>().channelvideoLoad();
  }

  final GlobalKey<ScaffoldState> _key = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return AppLoader(
      child: BlocConsumer<ChannelCubit, ChannelState>(
        listener: (context, state) {
          if (state is Loaded) {
            print('55555555${state.channeldetails!.data}');
            setState(() {
              channelvideos = state.channeldetails?.data as List<Datum>;
            });
          }
          // TODO: implement listener
        },
        builder: (context, state) {
          (state is Loading)
              ? context.loaderOverlay.show()
              : context.loaderOverlay.hide();
          return Scaffold(
            drawerEnableOpenDragGesture: false,
            appBar: AppBar(
              systemOverlayStyle:
                  const SystemUiOverlayStyle(statusBarColor: Colors.black),
              leading: IconButton(
                icon: Image.asset(
                  Images.menuBarIcon,
                  scale: 2.5,
                ),
                onPressed: () {
                  pushNewScreen(
                    context,
                    screen: DrawerWidget(),
                    withNavBar: false, // OPTIONAL VALUE. True by default.
                    pageTransitionAnimation: PageTransitionAnimation.slideRight,
                  );
                },
              ),
              centerTitle: true,
              toolbarHeight: 80,
              title: SizedBox(
                  height: 70,
                  child: Image.asset(
                    'assets/images/legacyleadersupdatelogo.png',
                  )),
              backgroundColor: Colors.black,
            ),
            body: Column(
              children: [
                Container(
                    height: 60,
                    color: const Color.fromARGB(255, 0, 0, 0),
                    width: double.infinity,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 15, vertical: 15),
                              child: SizedBox(
                                  height: 25,
                                  child: Image.asset(Images.channelButton)),
                            ),
                            Text('CHANNELS', style: textstyleheading),
                          ],
                        ),
                      ],
                    )),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(5),
                    child: GridView.builder(
                      itemCount: channelvideos.length,
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                              childAspectRatio: 3.11 / 2,
                              mainAxisSpacing: 5,
                              crossAxisSpacing: 5,
                              crossAxisCount: 2),
                      itemBuilder: (BuildContext context, int index) {
                        AppPref.setString('category$index',
                            channelvideos[index].id.toString());

                        return Padding(
                          padding: const EdgeInsets.all(0),
                          child: GestureDetector(
                            onTap: () {
                              Navigator.of(context).push(MaterialPageRoute(
                                  builder: (BuildContext context) =>
                                      ChannelsList(
                                        category:
                                            AppPref.getString('category$index'),
                                        name: channelvideos[index]
                                            .name
                                            .toString(),
                                      )));
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4),
                                  image: DecorationImage(
                                      colorFilter: ColorFilter.mode(
                                          Colors.black.withOpacity(0.3),
                                          BlendMode.darken),
                                      image: CachedNetworkImageProvider(
                                          channelvideos[index]
                                                  .thumbnail
                                                  .toString() ??
                                              'https://i.etsystatic.com/22794840/r/il/d3d2cf/**********/il_794xN.**********_j3ov.jpg'),
                                      fit: BoxFit.cover)),
                              child: Padding(
                                padding: const EdgeInsets.all(15.0),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      height: 30,
                                      width: 30,
                                      child:
                                          channelvideos[index].thumbnail != null
                                              ? Image.asset(Images.channelIcon)
                                              : Image.asset(''),
                                    ),
                                    Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 4),
                                        child: Text(
                                            channelvideos[index].thumbnail !=
                                                    null
                                                ? channelvideos[index]
                                                    .name
                                                    .toString()
                                                    .toUpperCase()
                                                : '',
                                            style: textstyle1))
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }
}
