part of 'channellist_cubit.dart';

@immutable
abstract class ChannellistState {}

class <PERSON>listInitial extends ChannellistState {}

class Loading extends ChannellistState {
  Loading();
}

class Loaded extends ChannellistState {
  final ChannelListVideoModel? channellistdetails;

  Loaded({this.channellistdetails});

  @override
  List<Object> get props => [];
}

class Message extends ChannellistState {
  final String message;

  Message(this.message);

  @override
  List<Object> get props => [];
}
