import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../../common_widgets/controllers/network_controller.dart';
import '../../../../utils/app_pref.dart';
import '../../../../utils/search.dart';
import '../../models/channellistmodel.dart';

part 'channellist_state.dart';

class ChannellistCubit extends Cubit<ChannellistState> {
  ChannellistCubit() : super(ChannellistInitial());

  Future<void> channellistvideoLoad(String category) async {
    emit(Loading());
    String? token = AppPref.getString('token');
    // String? cat_id='626bff2f204c55339bbbb2f5';
    print('qwer$token');

    emit(Loading());
    try {
      ChannelListVideoModel body =
          await NetworkController().channelListLoad('$token', category);
// alertmsg('${AppPref.getString('msg')}');
      print('333333');

      emit(Loaded(channellistdetails: body));
    } catch (e) {
      emit(Message(e.toString()));
      print('499999999${e.toString()}');
    }
  }
}
