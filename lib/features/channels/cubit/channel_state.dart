part of 'channel_cubit.dart';

@immutable
abstract class ChannelState {}

class ChannelInitial extends ChannelState {}

class Loading extends ChannelState {
  Loading();
}

class Loaded extends ChannelState {
  final ChannelVideoModel? channeldetails;

  Loaded({this.channeldetails});

  @override
  List<Object> get props => [];
}

class Message extends ChannelState {
  final String message;

  Message(this.message);

  @override
  List<Object> get props => [];
}
