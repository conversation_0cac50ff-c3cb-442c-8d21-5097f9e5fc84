import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../common_widgets/controllers/network_controller.dart';
import '../../../utils/app_pref.dart';
import '../models/channelsmain.dart';

part 'channel_state.dart';

class ChannelCubit extends Cubit<ChannelState> {
  ChannelCubit() : super(ChannelInitial());

  Future<void> channelvideoLoad() async {
    emit(Loading());
    String? token = AppPref.getString('token');
    print('qwer$token');

    emit(Loading());
    try {
      ChannelVideoModel body =
          await NetworkController().channelMainVideoLoad('$token');

// alertmsg('${AppPref.getString('msg')}');
      print('333333');

      emit(Loaded(channeldetails: body));
    } catch (e) {
      emit(Message(e.toString()));
      print('4444channels444${e.toString()}');
    }
  }
}
