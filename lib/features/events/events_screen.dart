import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:legacy/features/events/blocs/events_cubit.dart';
import 'package:legacy/features/events/event_item_screen.dart';
import 'package:legacy/features/events/models/event_item_model.dart';
import 'package:legacy/features/events/models/events_response_all.dart';
import 'package:legacy/main.dart';
import 'package:legacy/utils/app_pref.dart';
import 'package:legacy/utils/appcolors.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:legacy/utils/progress.dart';
import 'package:legacy/utils/timeformat.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';
import 'package:table_calendar/table_calendar.dart';

class EventsScreen extends StatefulWidget {
  const EventsScreen({Key? key}) : super(key: key);

  @override
  State<EventsScreen> createState() => _EventsScreenState();
}

class _EventsScreenState extends State<EventsScreen> {
  DateTime? _selectedDay;
  DateTime _focusedDay = DateTime.now();

  EventsResponseAll eventsResponse = EventsResponseAll();
  EventItemModel eventItemResponse = EventItemModel();

  Map<DateTime, List<Event>> resultMap = {};

  // final kEvents = LinkedHashMap<DateTime, List<Event>>(
  //   equals: isSameDay,
  //   hashCode: getHashCode,
  // )..addAll(_kEventSource);
  //
  // final _kEventSource = {
  //   for (var item in List.generate(50, (index) => index))
  //     DateTime.utc(kFirstDay.year, kFirstDay.month, item * 5): List.generate(
  //       item % 4 + 1,
  //       (index) => Event('Event $item | ${index + 1}'),
  //     )
  // }..addAll(
  //     {
  //       kToday: [
  //         Event('Today\'s Event 1'),
  //         Event('Today\'s Event 2'),
  //       ],
  //     },
  //   );
  //
  // int getHashCode(DateTime key) {
  //   return key.day * 1000000 + key.month * 10000 + key.year;
  // }
  //
  // List<DateTime> daysInRange(DateTime first, DateTime last) {
  //   final dayCount = last.difference(first).inDays + 1;
  //   return List.generate(
  //     dayCount,
  //     (index) => DateTime.utc(first.year, first.month, first.day + index),
  //   );
  // }
  //
  // final kToday = DateTime.now();
  // final kFirstDay = DateTime(kToday.year, kToday.month - 3, kToday.day);
  // final kLastDay = DateTime(kToday.year, kToday.month + 3, kToday.day);
  //

  List<Event> _getEventsForDay(DateTime day) {
    var newDateTime = DateTime(day.year, day.month, day.day);
    return resultMap[newDateTime] ?? [];
  }

  Map<DateTime, List<Event>> createMap(EventsResponseAll data) {
    for (var event in data.data!) {
      var startDate = DateTime(
          event.startDate!.year, event.startDate!.month, event.startDate!.day);

      if (!resultMap.containsKey(startDate)) {
        resultMap[startDate] = [const Event("")];
      } else {
        resultMap[startDate]!.add(const Event(""));
      }
    }

    return resultMap;
  }

  @override
  void initState() {
    super.initState();
    context.read<EventsCubit>().loadAllEvents(AppPref.getString("token") ?? "");
    var dateForApiCall =
        DateTime(_focusedDay.year, _focusedDay.month, _focusedDay.day);
    context.read<EventsCubit>().loadSelectedDaysEvents(
          token: AppPref.getString("token") ?? "",
          date: dateForApiCall.toIso8601String(),
          offset: "+5.30",
        );
  }

  @override
  Widget build(BuildContext context) {
    return AppLoader(
      child: BlocConsumer<EventsCubit, EventsState>(
        listener: (context, state) {
          if (state is Loaded) {
            if (state.events != null) {
              setState(() {
                eventsResponse = state.events!;
                createMap(state.events!);
              });
            }
            if (state.eventItem != null) {
              setState(() {
                eventItemResponse = state.eventItem!;
              });
            }
          }
        },
        builder: (context, state) {
          (state is Loading)
              ? context.loaderOverlay.show()
              : context.loaderOverlay.hide();
          return Scaffold(
            appBar: AppBar(
              backgroundColor: AppColors.colorDarkYellow,
              leading: IconButton(
                onPressed: () {
                  Navigator.of(context).pop();

                  // pushNewScreen(
                  //   context,
                  //   screen: const MainHome(),
                  //   withNavBar: false, // OPTIONAL VALUE. True by default.
                  //   pageTransitionAnimation: PageTransitionAnimation.cupertino,
                  // );
                },
                icon: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Image.asset("assets/images/backArrow.png"),
                ),
              ),
              title: const Text(
                "Events",
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              centerTitle: true,
              elevation: 0,
            ),
            body: Container(
              height: MediaQuery.of(context).size.height - 72,
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                bottom: 16,
              ),
              child: Column(
                children: [
                  TableCalendar(
                    firstDay: DateTime.utc(2010, 10, 16),
                    lastDay: DateTime.utc(2030, 3, 14),
                    focusedDay: _focusedDay,
                    daysOfWeekHeight: 32,
                    eventLoader: _getEventsForDay,
                    // calendarBuilders: CalendarBuilders(
                    //     markerBuilder: (context, dateTime, list) {
                    //   return const CircleAvatar(
                    //     child: Icon(
                    //       Icons.circle,
                    //       color: Colors.black,
                    //       size: 2,
                    //     ),
                    //     radius: 2,
                    //   );
                    // }),
                    selectedDayPredicate: (dateTime) {
                      return dateTime == _selectedDay;
                    },
                    onDaySelected: (selectedDay, focusedDay) {
                      setState(() {
                        _selectedDay = selectedDay;
                        _focusedDay = focusedDay;
                      });
                      var dateForApiCall = DateTime(_selectedDay!.year,
                          _selectedDay!.month, _selectedDay!.day);
                      context.read<EventsCubit>().loadSelectedDaysEvents(
                            token: AppPref.getString("token") ?? "",
                            date: dateForApiCall.toIso8601String(),
                            offset: "+5.30",
                          );
                    },
                    daysOfWeekStyle: DaysOfWeekStyle(
                      dowTextFormatter: (dateTime, locale) {
                        final format = DateFormat.E(locale).add_jms();
                        final weekday = format.format(dateTime);
                        return weekday.substring(0, 1);
                      },
                      weekdayStyle: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                      weekendStyle: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    headerStyle: HeaderStyle(
                      titleCentered: true,
                      titleTextStyle: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                      formatButtonVisible: false,
                      leftChevronIcon: Icon(
                        Icons.arrow_left,
                        color: AppColors.colorPrimary,
                      ),
                      rightChevronIcon: Icon(
                        Icons.arrow_right,
                        color: AppColors.colorPrimary,
                      ),
                    ),
                    calendarStyle: CalendarStyle(
                      markersAlignment: Alignment.topCenter,
                      selectedDecoration: const BoxDecoration(
                        color: Color(0xFFF0EEFC),
                        shape: BoxShape.circle,
                      ),
                      markersAnchor: 2,
                      markerDecoration: BoxDecoration(
                        color: AppColors.colorNavyBlue,
                        shape: BoxShape.circle,
                      ),
                      markerSize: 4,
                      todayDecoration: const BoxDecoration(
                        color: Color(0xFFF0EEFD),
                        shape: BoxShape.circle,
                      ),
                      markersMaxCount: 1,
                      selectedTextStyle:
                          const TextStyle(color: Color(0xFF9074F3)),
                      todayTextStyle: const TextStyle(color: Color(0xFFF8754E)),
                    ),
                  ),
                  gapH32,
                  const Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      "Scheduled Events",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Expanded(
                    child: eventItemResponse.data != null &&
                            eventItemResponse.data!.isNotEmpty
                        ? ListView.builder(
                            itemCount: eventItemResponse.data?.length ?? 0,
                            itemBuilder: (context, index) {
                              return GestureDetector(
                                onTap: () {
                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder: (context) => EventItemScreen(
                                        data: eventItemResponse.data![index],
                                      ),
                                    ),
                                  );
                                },
                                child: Container(
                                  height: 80,
                                  margin:
                                      const EdgeInsets.only(top: 8, bottom: 8),
                                  decoration: BoxDecoration(
                                    color: index % 2 != 0
                                        ? const Color(0xFFF6E1B2)
                                        : const Color(0xFFB2F5EC),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        flex: 1,
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              convertUtcToLocalTime(
                                                  '${eventItemResponse.data?[index].startDate.toString()}'),
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            gapH8,
                                            Text(
                                              convertUtcToLocalTime(
                                                  eventItemResponse
                                                          .data?[index].endDate
                                                          ?.toIso8601String() ??
                                                      ""),
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Colors.black54,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const VerticalDivider(
                                        color: Colors.red,
                                        thickness: 2,
                                        indent: 8,
                                        endIndent: 8,
                                      ),
                                      gapW8,
                                      Expanded(
                                        flex: 3,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              eventItemResponse
                                                      .data?[index].title ??
                                                  "",
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            gapH16,
                                            Text(eventItemResponse
                                                    .data?[index].description ??
                                                "")
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              );
                            },
                          )
                        : const Center(
                            child: Text(
                              "No Events Are Available",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class Event {
  final String title;

  const Event(this.title);

  @override
  String toString() => title;
}
