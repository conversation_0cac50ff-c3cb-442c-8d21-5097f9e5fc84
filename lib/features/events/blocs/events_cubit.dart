import 'package:bloc/bloc.dart';
import 'package:legacy/features/events/models/event_item_model.dart';
import 'package:legacy/features/events/models/events_response_all.dart';

import '../../../common_widgets/controllers/network_controller.dart';

part 'events_state.dart';

class EventsCubit extends Cubit<EventsState> {
  EventsCubit() : super(Loading());

  var networkController = NetworkController();

  Future<void> loadAllEvents(String token) async {
    emit(Loading());
    try {
      EventsResponseAll events = await networkController.getAllEvents(token);
      emit(Loaded(events: events));
    } catch (e) {
      emit(Message(message: e.toString()));
    }
  }

  Future<void> loadSelectedDaysEvents(
      {required String token,
      required String date,
      required String offset}) async {
    emit(Loading());
    try {
      EventItemModel eventItem =
          await networkController.getSelectedDaysEvents(token, date, offset);
      emit(Loaded(eventItem: eventItem));
    } catch (e) {
      emit(Message(message: e.toString()));
    }
  }
}
