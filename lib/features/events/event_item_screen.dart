import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:legacy/features/events/models/event_item_model.dart';
import 'package:legacy/utils/gaps.dart';
// import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import '../../main.dart';
import '../../utils/appcolors.dart';
import '../../utils/timeformat.dart';
import '../homepage/home.dart';

class EventItemScreen extends StatelessWidget {
  const EventItemScreen({Key? key, required this.data}) : super(key: key);

  final Datum data;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.colorDarkYellow,
        leading: IconButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          icon: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Image.asset("assets/images/backArrow.png"),
          ),
        ),
        title: const Text(
          "Event Details",
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: Container(
        height: MediaQuery.of(context).size.height - 72,
        padding: const EdgeInsets.only(
          left: 16,
          right: 16,
          bottom: 16,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            gapH16,
            SizedBox(
              height: MediaQuery.of(context).size.width / 1.75,
              width: MediaQuery.of(context).size.width,
              child: CachedNetworkImage(
                imageUrl: data.image!,
                imageBuilder: (context, imageProvider) => Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    image: DecorationImage(
                      image: imageProvider,
                      fit: BoxFit.cover,
                      colorFilter: const ColorFilter.mode(
                        Colors.grey,
                        BlendMode.colorBurn,
                      ),
                    ),
                  ),
                ),
                placeholder: (context, url) =>
                    const Center(child: CircularProgressIndicator()),
                errorWidget: (context, url, error) =>
                    const Center(child: Icon(Icons.error)),
              ),
            ),
            gapH12,
            Text(
              data.title!,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 24,
              ),
            ),
            gapH12,
            Text(
              formatDate(data.startDate!.toIso8601String()),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Colors.black54,
              ),
            ),
            gapH8,
            Text(
              formatTimeRange(data.startDate!.toIso8601String(),
                  data.endDate!.toIso8601String()),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Colors.black54,
              ),
            ),
            gapH16,
            Row(
              children: [
                const Text(
                  "Notes",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                Expanded(
                    child: Divider(
                  color: AppColors.colorDarkYellow,
                  height: 1,
                  thickness: 1,
                  indent: 8,
                  endIndent: 0,
                )),
              ],
            ),
            gapH12,
            Text(
              data.description!,
              style: const TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 16,
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}
