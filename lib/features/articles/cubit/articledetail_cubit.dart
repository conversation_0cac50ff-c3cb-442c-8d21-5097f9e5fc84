import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../common_widgets/controllers/network_controller.dart';
import '../../../utils/app_pref.dart';
import '../models/articledetailmodel.dart';

part 'articledetail_state.dart';

class ArticledetailCubit extends Cubit<ArticledetailState> {
  ArticledetailCubit() : super(ArticledetailInitial());
  Future<void> articlesdetailLoad(String articleId) async {
    emit(Loading());
    String? token = AppPref.getString('token');
    // String? cat_id='626bff2f204c55339bbbb2f5';
    print('qwer$token');

    emit(Loading());
    try {
      await NetworkController().articleMarkAsRead('$token', articleId);

      ArticlesDetailModel body =
          await NetworkController().articledetails('$token', articleId);

// alertmsg('${AppPref.getString('msg')}');
      print('2-2-2-2-2');
      print('000000--------${body.data}');

      emit(Loaded(articledetails: body));
    } catch (e) {
      emit(Message(e.toString()));
      print('4444444${e.toString()}');
    }
  }
}
