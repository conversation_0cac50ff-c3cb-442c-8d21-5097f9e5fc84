part of 'articledetail_cubit.dart';

@immutable
abstract class ArticledetailState {}

class ArticledetailInitial extends ArticledetailState {}

class Loading extends ArticledetailState {
  Loading();
}

class Loaded extends ArticledetailState {
  final ArticlesDetailModel? articledetails;

  Loaded({this.articledetails});

  @override
  List<Object> get props => [];
}

class Message extends ArticledetailState {
  final String message;

  Message(this.message);

  @override
  List<Object> get props => [];
}
