import 'package:bloc/bloc.dart';
import 'package:legacy/features/articles/models/articlesmodel.dart';
import 'package:meta/meta.dart';

import '../../../common_widgets/controllers/network_controller.dart';
import '../../../utils/app_pref.dart';

part 'articles_state.dart';

class ArticlesCubit extends Cubit<ArticlesState> {
  ArticlesCubit() : super(ArticlesInitial());
  Future<void> articleslistLoad() async {
    emit(Loading());
    String? token = AppPref.getString('token');
    // String? cat_id='626bff2f204c55339bbbb2f5';
    print('qwer$token');

    emit(Loading());
    try {
      ArticlesModel body = await NetworkController().getarticles('$token');

// alertmsg('${AppPref.getString('msg')}');
      print('333333');
      print('000000--------${body.data}');

      emit(Loaded(articlelistdetails: body));
    } catch (e) {
      emit(Message(e.toString()));
      print('4444444${e.toString()}');
    }
  }
}
