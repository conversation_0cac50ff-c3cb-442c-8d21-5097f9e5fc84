part of 'articles_cubit.dart';

@immutable
abstract class ArticlesState {}

class ArticlesInitial extends ArticlesState {}

class Loading extends ArticlesState {
  Loading();
}

class Loaded extends ArticlesState {
  final ArticlesModel? articlelistdetails;

  Loaded({this.articlelistdetails});

  @override
  List<Object> get props => [];
}

class Message extends ArticlesState {
  final String message;

  Message(this.message);

  @override
  List<Object> get props => [];
}
