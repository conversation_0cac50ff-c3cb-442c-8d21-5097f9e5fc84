// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/features/articles/cubit/articledetail_cubit.dart';
import 'package:legacy/utils/app_styles.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:loader_overlay/loader_overlay.dart';

import '../../../utils/appcolors.dart';
import '../../../utils/images.dart';
import '../../../utils/progress.dart';
import '../../../utils/timeformat.dart';
import 'models/articledetailmodel.dart';
import 'package:flutter_html/flutter_html.dart';

class ArticleDetail extends StatefulWidget {
  const ArticleDetail({super.key, this.articleId});
  final articleId;
  @override
  State<ArticleDetail> createState() => _ArticleDetailState();
}

class _ArticleDetailState extends State<ArticleDetail> {
  ArticlesDetailModel? articledetail;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<ArticledetailCubit>().articlesdetailLoad(widget.articleId);
  }

  @override
  Widget build(BuildContext context) {
    return AppLoader(
        child: BlocConsumer<ArticledetailCubit, ArticledetailState>(
            listener: (context, state) {
      if (state is Loaded) {
        print('5-------5${state.articledetails}');
        setState(() {
          articledetail = state.articledetails;
        });
      }
      // TODO: implement listener
    }, builder: (context, state) {
      String date = articledetail?.data?.createdAt.toString() ??
          '2023-04-17T16:51:51.897Z';

      (state is Loading)
          ? context.loaderOverlay.show()
          : context.loaderOverlay.hide();
      return Scaffold(
        backgroundColor: AppColors.colorDarkYellow,
          appBar: AppBar(
            backgroundColor: AppColors.colorDarkYellow,
            title: const Text(
              'Article Details',
              style: TextStyle(color: Colors.black,fontSize: 18,fontWeight: FontWeight.bold),
            ),
            centerTitle: true,
            leading: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: SizedBox(
                    height: 10,
                    child: Image.asset('assets/images/backArrow.png')),
              ),
            ),
          ),
          body: Container(color: Colors.white,
            child: articledetail?.data?.title != null
                ? Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: ListView(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        gapH12,
                        Text(
                          '${articledetail?.data?.title ?? ''}',
                          style: const TextStyle(
                              fontSize: 20, fontWeight: FontWeight.bold),
                        ),
                        gapH32,
                        Row(
                          children: [
                            SizedBox(
                                height: 40,
                                child: Image.asset(
                                  'assets/images/icon_reciever.png',
                                )),
                            gapW12,
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    articledetail?.data?.author ?? "",
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                                Text(dayChanger(date)),
                              ],
                            )
                          ],
                        ),
                        gapH24,
                        Container(
                          width: double.infinity,
                          height: 240,
                          decoration: BoxDecoration(
                              image: DecorationImage(
                                  image: CachedNetworkImageProvider(
                                      '${articledetail?.data?.articleImage}'),
                                  fit: BoxFit.fitWidth),
                              borderRadius: BorderRadius.circular(8),
                              // border: Border.all(color: Colors.black),
                              color: Colors.white),
                        ),
                        gapH4,
                        Html(
                          data: """${articledetail?.data?.articleData ?? ''}""",
                        )
                      ],
                    ),
                  )
                : Container(
                    child: Center(
                      child: Text(
                        'Nothing to show',
                        style:
                            TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ),
                    color: Colors.white,
                    height: double.infinity,
                    width: double.infinity),
          ));
    }));
  }
}
