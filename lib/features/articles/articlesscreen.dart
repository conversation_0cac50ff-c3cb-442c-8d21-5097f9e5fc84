import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/features/articles/cubit/articles_cubit.dart';
import 'package:legacy/features/articles/models/articlesmodel.dart';
import 'package:legacy/utils/app_styles.dart';
import 'package:legacy/utils/appcolors.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:loader_overlay/loader_overlay.dart';
// import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import '../../main.dart';
import '../../utils/progress.dart';
import '../../utils/timeformat.dart';
import '../homepage/home.dart';
import 'articlesdetails.dart';

class Articlespage extends StatefulWidget {
  const Articlespage({super.key});

  @override
  State<Articlespage> createState() => _ArticlespageState();
}

class _ArticlespageState extends State<Articlespage> {
  ArticlesModel? articles;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<ArticlesCubit>().articleslistLoad();
  }

  @override
  Widget build(BuildContext context) {
    return AppLoader(
        child: BlocConsumer<ArticlesCubit, ArticlesState>(
            listener: (context, state) {
      if (state is Loaded) {
        setState(() {
          articles = state.articlelistdetails;
        });
      }
      // TODO: implement listener
    }, builder: (context, state) {
      (state is Loading)
          ? context.loaderOverlay.show()
          : context.loaderOverlay.hide();
      return Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.colorDarkYellow,
          title: const Text(
            'Articles',
            style: TextStyle(color: Colors.black,fontSize: 18,fontWeight: FontWeight.bold),
          ),
          centerTitle: true,
          leading: GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: SizedBox(
                  height: 10,
                  child: Image.asset('assets/images/backArrow.png')),
            ),
          ),
        ),
        body: articles?.data?.result![0].thumbnail == null
            ? Container(
                child: Center(
                  child: Text(
                    'Nothing to show',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                color: Colors.white,
                height: double.infinity,
                width: double.infinity)
            : ListView.builder(
                itemBuilder: (BuildContext context, index) {
                  String date =
                      articles?.data?.result![index].createdAt.toString() ??
                          '2023-04-17T16:51:51.897Z';
                  return GestureDetector(
                    onTap: () => Navigator.of(context)
                        .push(MaterialPageRoute(
                            builder: (BuildContext context) => ArticleDetail(
                                  articleId: articles?.data?.result?[index].id,
                                )))
                        .then((value) =>
                            context.read<ArticlesCubit>().articleslistLoad()),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: SizedBox(
                        height: 140,
                        child: Card(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          elevation: 5,
                          // margin: EdgeInsets.all(10),
                          child: Padding(
                            padding: const EdgeInsets.only(
                                left: 8.0, right: 8, top: 5, bottom: 5),
                            child: Row(children: [
                              Column(
                                children: [
                                  Container(
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          image: DecorationImage(
                                            image: CachedNetworkImageProvider(
                                                '${articles?.data?.result![index].thumbnail}'),
                                            fit: BoxFit.cover,
                                          )),
                                      height: 118,
                                      width: 130,
                                      child: articles?.data?.result![index]
                                                  .markAsRead ==
                                              false
                                          ? const Align(
                                              alignment: Alignment.topRight,
                                              child: Row(
                                                children: [
                                                  Spacer(),
                                                  CircleAvatar(
                                                    backgroundColor: Colors.red,
                                                    radius: 5,
                                                  )
                                                ],
                                              ),
                                            )
                                          : const Text(''))
                                ],
                              ),
                              gapW8,
                              SizedBox(
                                height: 125,
                                width: MediaQuery.of(context).size.width/2,
                                child: Column(
                                  children: [
                                    Align(
                                      alignment: Alignment.topLeft,
                                      child: Text(
                                        '${articles?.data?.result![index].title ?? ''}',
                                        style: textstyle3,
                                      ),
                                    ),
                                    gapH24,
                                    Row(
                                      children: [
                                        SizedBox(
                                            height: 40,
                                            child: articles
                                                        ?.data
                                                        ?.result![index]
                                                        .title !=
                                                    null
                                                ? Image.asset(
                                                    'assets/images/icon_reciever.png',
                                                  )
                                                : Text('')),
                                        gapW12,
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Align(
                                              alignment: Alignment.topLeft,
                                              child: Text(
                                                '${articles?.data?.result![index].author ?? ''}',
                                                style: const TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                            ),
                                            articles?.data?.result![index]
                                                        .title !=
                                                    null
                                                ? Text(dayChanger(date))
                                                : Text(''),
                                          ],
                                        )
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ]),
                          ),
                        ),
                      ),
                    ),
                  );
                },
                itemCount: articles?.data?.total,
              ),
      );
    }));
  }
}
