// To parse this JSON data, do
//
//     final articlesDetailModel = articlesDetailModelFromJson(jsonString);

import 'dart:convert';

ArticlesDetailModel articlesDetailModelFromJson(String str) =>
    ArticlesDetailModel.fromJson(json.decode(str));

String articlesDetailModelToJson(ArticlesDetailModel data) =>
    json.encode(data.toJson());

class ArticlesDetailModel {
  bool? status;
  String? message;
  Data? data;

  ArticlesDetailModel({
    this.status,
    this.message,
    this.data,
  });

  factory ArticlesDetailModel.fromJson(Map<String, dynamic> json) =>
      ArticlesDetailModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  String? id;
  String? title;
  String? author;
  String? articleData;
  String? articleImage;
  DateTime? createdAt;

  Data({
    this.id,
    this.title,
    this.author,
    this.articleData,
    this.articleImage,
    this.createdAt,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        id: json["_id"],
        title: json["title"],
        author: json["author"],
        articleData: json["articleData"],
        articleImage: json["articleImage"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "title": title,
        "author": author,
        "articleData": articleData,
        "articleImage": articleImage,
        "createdAt": createdAt?.toIso8601String(),
      };
}
