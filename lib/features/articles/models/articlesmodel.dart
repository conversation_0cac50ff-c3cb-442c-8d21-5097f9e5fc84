// To parse this JSON data, do
//
//     final articlesModel = articlesModelFromJson(jsonString);

import 'dart:convert';

ArticlesModel articlesModelFromJson(String str) =>
    ArticlesModel.fromJson(json.decode(str));

String articlesModelToJson(ArticlesModel data) => json.encode(data.toJson());

class ArticlesModel {
  bool? status;
  String? message;
  Data? data;

  ArticlesModel({
    this.status,
    this.message,
    this.data,
  });

  factory ArticlesModel.fromJson(Map<String, dynamic> json) => ArticlesModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  List<Result>? result;
  int? total;

  Data({
    this.result,
    this.total,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        result: json["result"] == null
            ? []
            : List<Result>.from(json["result"]!.map((x) => Result.fromJson(x))),
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "result": result == null
            ? []
            : List<dynamic>.from(result!.map((x) => x.toJson())),
        "total": total,
      };
}

class Result {
  String? id;
  String? title;
  String? author;
  String? thumbnail;
  bool? markAsRead;
  DateTime? createdAt;

  Result({
    this.id,
    this.title,
    this.author,
    this.thumbnail,
    this.markAsRead,
    this.createdAt,
  });

  factory Result.fromJson(Map<String, dynamic> json) => Result(
        id: json["_id"],
        title: json["title"],
        author: json["author"],
        thumbnail: json["thumbnail"],
        markAsRead: json["markAsRead"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "title": title,
        "author": author,
        "thumbnail": thumbnail,
        "markAsRead": markAsRead,
        "createdAt": createdAt?.toIso8601String(),
      };
}
