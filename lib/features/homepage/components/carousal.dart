import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:legacy/common_widgets/videoplayer/videoplayer.dart';
import 'package:legacy/features/homepage/model/recent_videomodel.dart';
import 'package:legacy/utils/app_styles.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import '../../../utils/app_pref.dart';
import '../../../utils/images.dart';

class Caro extends StatefulWidget {
  final List<Datum> recentVideos;

  const Caro({super.key, required this.recentVideos});

  @override
  State<Caro> createState() => _CaroState();
}

class _CaroState extends State<Caro> {
  List<Datum> recent = [];

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.only(left: 6),
        child:
            // ? CarouselSlider(
            //     options: CarouselOptions(
            //       // enlargeFactor: .3,
            //       enlargeStrategy: CenterPageEnlargeStrategy.height,
            //       enlargeCenterPage: true,
            //       scrollPhysics: const BouncingScrollPhysics(),
            //       height: 180,
            //       viewportFraction: .9,
            //       // padEnds: false,
            //       autoPlay: false,
            //       pauseAutoPlayInFiniteScroll: true,
            //       enableInfiniteScroll: false,
            //       autoPlayAnimationDuration: const Duration(milliseconds: 1000),
            //       scrollDirection: Axis.horizontal,
            //       autoPlayCurve: Curves.linear,
            //     ),
            //     items: widget.recentVideos
            //         .map((item) => Padding(
            //               padding: const EdgeInsets.only(left: 8),
            //               child: GestureDetector(
            //                 onTap: () {
            //                   AppPref.setString('vimeoId', item.vimeoId ?? 'j');

            //                   // String? token1 = AppPref.getString('token');
            //                   pushNewScreen(
            //                     context,
            //                     screen: VideoPlayerPage(),
            //                     withNavBar:
            //                         false, // OPTIONAL VALUE. True by default.
            //                     pageTransitionAnimation:
            //                         PageTransitionAnimation.cupertino,
            //                   );
            //                 },
            //                 child: Container(
            //                   width: MediaQuery.of(context).size.width,
            //                   decoration: BoxDecoration(
            //                       borderRadius: BorderRadius.circular(8),
            //                       image: DecorationImage(
            //                           image: CachedNetworkImageProvider(
            //                               '${item.thumbnail}'),
            //                           fit: BoxFit.cover)),
            //                   child: Padding(
            //                     padding: const EdgeInsets.all(8.0),
            //                     child: Column(
            //                         mainAxisAlignment: MainAxisAlignment.center,
            //                         children: [
            //                           Padding(
            //                             padding: const EdgeInsets.symmetric(
            //                                 vertical: 20, horizontal: 15),
            //                             child: Align(
            //                               alignment: Alignment.centerLeft,
            //                               child: Text(
            //                                   item.thumbnail != null
            //                                       ? '${item.title}'
            //                                           .toUpperCase()
            //                                       : '',
            //                                   style: textstylethumbnail),
            //                             ),
            //                           ),
            //                           Padding(
            //                             padding: const EdgeInsets.symmetric(
            //                                 horizontal: 15),
            //                             child: Row(
            //                               children: [
            //                                 SizedBox(
            //                                     height: 40,
            //                                     width: 40,
            //                                     child: item.thumbnail == null
            //                                         ? Image.network('')
            //                                         : Image.asset(
            //                                             Images.playIcon,
            //                                           )),
            //                                 Padding(
            //                                   padding: const EdgeInsets.only(
            //                                       left: 15),
            //                                   child: Text(
            //                                     '${item.duration} ',
            //                                     style: textstylesubthumbnail,
            //                                   ),
            //                                 ),
            //                                 Text('MINUTES',
            //                                     style: textstylesubthumbnail),
            //                               ],
            //                             ),
            //                           )
            //                         ]),
            //                   ),
            //                 ),
            //               ),
            //             ))
            //         .toList()
            //     //         items: recent.map((item, index) => Container(
            //     //   color: Colors.green,
            //     //   child: Center(child: Text('${response[index].thumbnail}')),
            //     // ) as Function(Datum e)).toList()
            //     // ,

            //     // for (var i = 0; i < recent!.data!.length.toInt(); i++)
            //     //   Container(
            //     //       decoration: BoxDecoration(color: Colors.blue,
            //     //         image: DecorationImage(
            //     //           image: NetworkImage('${recent?.data![i].thumbnail}'),
            //     //           fit: BoxFit.fitHeight,
            //     //         ),
            //     //         // border:
            //     //         //     Border.all(color: Theme.of(context).accentColor),
            //     //         borderRadius: BorderRadius.circular(32.0),
            //     //       ),
            //     //     ),             ]
            //     // Container(
            //     //   width: MediaQuery.of(context).size.width,
            //     //   margin: const EdgeInsets.all(10),
            //     //   decoration: BoxDecoration(
            //     //       borderRadius: BorderRadius.circular(20), color: Colors.blue),
            //     // ),
            //     // Container(
            //     //   width: MediaQuery.of(context).size.width,
            //     //   margin: const EdgeInsets.all(10),
            //     //   decoration: BoxDecoration(
            //     //       borderRadius: BorderRadius.circular(20),
            //     //       color: const Color.fromARGB(255, 199, 16, 175)),
            //     // ),
            //     // Container(
            //     //   width: MediaQuery.of(context).size.width,
            //     //   margin: const EdgeInsets.all(10),
            //     //   decoration: BoxDecoration(
            //     //       borderRadius: BorderRadius.circular(20),
            //     //       color: const Color.fromARGB(255, 4, 219, 33)),
            //     // ),
            //     )
            // :
              SizedBox(
                height: 200,
                child: Center(child: Text('No recent videos are available')),
              ));
  }
}
