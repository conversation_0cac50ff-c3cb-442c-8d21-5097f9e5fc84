import 'package:flutter/material.dart';
import 'package:legacy/features/channels/channels.dart';
import 'package:legacy/utils/constants.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import '../../../main.dart';
import '../../announcements/screens/announcement.dart';
import '../../articles/articlesscreen.dart';
import '../../events/events_screen.dart';
import '../../message/messagescreen.dart';
import '../../podcasts/screens/podcast.dart';

class Row1 extends StatelessWidget {
  const Row1({super.key});

  @override
  Widget build(BuildContext context) {
    var pagelist = [
      const channels(),
      const Podcasts(),
      const Announcements(),
      const Articlespage(),
      const MessageScreen(),
      const EventsScreen(),
    ];

    return Padding(
      padding: const EdgeInsets.only(left: 6),
      child: SizedBox(
        height: 150,
        width: double.infinity,
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: ListView.builder(
              scrollDirection: Axis.horizontal,
              shrinkWrap: true,
              itemCount: scrollImage.length,
              itemBuilder: (BuildContext context, index) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: GestureDetector(
                    onTap: () {
                      if (index == 0) {
                        controller.jumpToTab(1);
                      }
                      if (index == 1) {
                        controller.jumpToTab(2);
                      }
                      if (index == 2) {
                        controller.jumpToTab(3);
                      }
                      if (index == 3) {
                        pushNewScreen(
                          context,
                          screen: Articlespage(),
                          withNavBar: false, // OPTIONAL VALUE. True by default.
                          pageTransitionAnimation:
                              PageTransitionAnimation.cupertino,
                        );
                      }
                      if (index == 4) {
                        pushNewScreen(
                          context,
                          screen: MessageScreen(),
                          withNavBar: false, // OPTIONAL VALUE. True by default.
                          pageTransitionAnimation:
                              PageTransitionAnimation.cupertino,
                        );
                      }
                      if (index == 5) {
                        pushNewScreen(context,
                            screen: EventsScreen(),
                            withNavBar:
                                false, // OPTIONAL VALUE. True by default.
                            pageTransitionAnimation:
                                PageTransitionAnimation.cupertino);
                      }
                    },
                    child: Container(
                      width: 140,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                            colorFilter: ColorFilter.mode(
                                Colors.black.withOpacity(0.1),
                                BlendMode.darken),
                            image: AssetImage(scrollImage[index]),
                            fit: BoxFit.cover),
                        // border: Border.all(
                        //     // color: Colors.transparent
                        //     ),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(8)),
                      ),
                      child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 32),
                              child: Center(
                                child: Text(
                                  scrollNames[index],
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14),
                                ),
                              ),
                            )
                          ]),
                    ),
                  ),
                );
              }),
        ),
      ),
    );
  }
}
