import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../common_widgets/controllers/network_controller.dart';
import '../../../utils/app_pref.dart';
import '../model/recent_videomodel.dart';

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  HomeCubit() : super(Loading());

  Future<void> recentvideoLoad() async {
    emit(Loading());
    String? token = AppPref.getString('token');
    print('qwer$token');

    emit(Loading());
    try {
      RecentModel body = await NetworkController().recentVideoLoad('$token');

// alertmsg('${AppPref.getString('msg')}');

      emit(Loaded(recentDetails: body));
    } catch (e) {
      emit(Message(e.toString()));
    }
  }
}
