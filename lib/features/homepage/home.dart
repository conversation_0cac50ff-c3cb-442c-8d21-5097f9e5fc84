import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/common_widgets/sidedrawer/side_drawer.dart';
import 'package:legacy/features/channels/channels.dart';
import 'package:legacy/features/homepage/model/recent_videomodel.dart';
import 'package:legacy/utils/app_styles.dart';
import 'package:legacy/utils/progress.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import '../../utils/app_pref.dart';
import '../../utils/images.dart';
import 'components/carousal.dart';
import 'components/row1.dart';
import 'cubit/home_cubit.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  List<Datum> recent = [];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // String? vimeoId=AppPref.getString('vimeo_id');
    print("-------22222-----------${AppPref.getString('token')}");
    context.read<HomeCubit>().recentvideoLoad();
  }

  final GlobalKey<ScaffoldState> _key = GlobalKey();

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    return AppLoader(
      child: BlocConsumer<HomeCubit, HomeState>(listener: (context, state) {
        print("111111111111111$state");
        if (state is Message) {}
        if (state is Loaded) {
          //  setState(() {
          recent = state.recentDetails?.data as List<Datum>;
          //  });
        }
      }, builder: (context, state) {
        (state is Loading)
            ? context.loaderOverlay.show()
            : context.loaderOverlay.hide();
        return Scaffold(
          endDrawerEnableOpenDragGesture: false,
          appBar: AppBar(
            systemOverlayStyle:
                const SystemUiOverlayStyle(statusBarColor: Colors.black),
            leading: IconButton(
              icon: Image.asset(
                Images.menuBarIcon,
                scale: 2.5,
              ),
              onPressed: () {
                pushNewScreen(
                  context,
                  screen: DrawerWidget(),
                  withNavBar: false, // OPTIONAL VALUE. True by default.
                  pageTransitionAnimation: PageTransitionAnimation.slideRight,
                );
                // _key.currentState!.openDrawer();
              },
            ),
            centerTitle: true,
            toolbarHeight: 80,
            title: SizedBox(
                height: 70,
                child: Image.asset(
                  'assets/images/legacyleadersupdatelogo.png',
                )),
            backgroundColor: Colors.black,
          ),
          body: ListView(children: [
            Container(
                height: MediaQuery.of(context).size.height / 7,
                color: const Color.fromARGB(255, 0, 0, 0),
                width: double.infinity,
                child: Center(
                  child: Center(
                      child: Text('RECLAIMING MANHOOD', style: textstyle2)),
                )),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text('LATEST VIDEOS', style: textstyle3),
            ),
            Caro(
              recentVideos: recent,
            ),
            Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text('CATEGORIES', style: textstyle3)),
            const Row1(),
          ]),
        );
      }),
    );
  }
}
