part of 'forget_password_cubit.dart';

abstract class ForgetPasswordState {}

class ForgetPasswordInitial extends ForgetPasswordState {}

class Loading extends ForgetPasswordState {
  @override
  List<Object> get props => [];
}

class Loaded extends ForgetPasswordState {
  final ForgetPsw? ForgetDetails;

  Loaded({
    this.ForgetDetails,
  });

  @override
  List<Object> get props => [];
}

class Message extends ForgetPasswordState {
  final String? msg;

  Message(this.msg);

  @override
  List<Object> get props => [];
}
