import 'package:bloc/bloc.dart';

import '../../../common_widgets/controllers/network_controller.dart';
import '../model/forgetpwd_model.dart';

part 'forget_password_state.dart';

class ForgetPasswordCubit extends Cubit<ForgetPasswordState> {
  ForgetPasswordCubit() : super(ForgetPasswordInitial());

  Future<void> forgetLogin(String email) async {
    emit(ForgetPasswordInitial());
    try {
      ForgetPsw body = await NetworkController().forgetPwd(email);

      emit(Loaded(ForgetDetails: body));
    } catch (e) {
      emit(Message(e.toString()));
      print(e);
    }
  }
}
