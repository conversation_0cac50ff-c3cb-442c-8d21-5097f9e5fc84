// To parse this JSON data, do
//
//     final forgetPsw = forgetPswFromJson(jsonString);

import 'dart:convert';

ForgetPsw forgetPswFromJson(String str) => ForgetPsw.fromJson(json.decode(str));

String forgetPswToJson(ForgetPsw data) => json.encode(data.toJson());

class ForgetPsw {
  ForgetPsw({
    required this.status,
    required this.message,
  });

  bool status;
  String message;

  factory ForgetPsw.fromJson(Map<String, dynamic> json) => ForgetPsw(
        status: json["status"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
      };
}
