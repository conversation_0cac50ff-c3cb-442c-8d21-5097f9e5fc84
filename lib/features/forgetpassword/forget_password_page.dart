import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:legacy/utils/progress.dart';
import 'package:loader_overlay/loader_overlay.dart';

import '../../common_widgets/tools.dart';
import '../../utils/appcolors.dart';
import '../../utils/images.dart';
import 'cubit/forget_password_cubit.dart';

class ForgetPasswordPage extends StatefulWidget {
  const ForgetPasswordPage({super.key});

  @override
  State<ForgetPasswordPage> createState() => _ForgetPasswordPageState();
}

class _ForgetPasswordPageState extends State<ForgetPasswordPage> {
  TextEditingController emailcontroller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ForgetPasswordCubit(),
      child: App<PERSON>oader(
        child: Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: true,
            leading: IconButton(
              icon: Image.asset(
                'assets/images/backArrow.png',
                height: 15,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            title: const Text(
              'Forgot Password',
              style: TextStyle(color: Colors.black),
            ),
            centerTitle: true,
            backgroundColor: const Color(0xffE0BE6B),
          ),
          body: BlocConsumer<ForgetPasswordCubit, ForgetPasswordState>(
            listener: (context, state) {
              if (state is Loaded) {
                alertmsg(state.ForgetDetails?.message, context);
                if (state.ForgetDetails?.status ?? false) {
                  // AppPref.setString('usr', username.toString());
                  // AppPref.setString('LOGIN_DETAILS', response.data.toString());
                  // AppPref.setString('token', response.data['data']['token']);
                  // AppPref.setString('ref_token', response.data['data']['refreshToken']);
                  // AppPref.setString('usr_id', response.data['data']['userId']);
                  // AppPref.setString('vimeo_id', response.data['data']['vimeoId']);
                }
              }
              (state is Loading)
                  ? context.loaderOverlay.show()
                  : context.loaderOverlay.hide();
              // TODO: implement listener
            },
            builder: (context, state) {
              return Padding(
                padding: const EdgeInsets.all(15.0),
                child: SingleChildScrollView(
                  child: Container(
                    height: MediaQuery.of(context).size.height,
                    color: Colors.white,
                    child: Column(children: [
                      gapH32,
                      Center(
                        child: Image.asset(
                          Images.bg,
                          width: 120,
                          height: 120,
                        ),
                      ),
                      gapH32,
                      gapH32,
                      gapH32,
                      Form(
                        autovalidateMode: AutovalidateMode.always,
                        child: TextFormField(
                          validator: validateEmail,
                          controller: emailcontroller,
                          decoration: InputDecoration(
                              prefixIcon: Padding(
                                padding: const EdgeInsets.all(15.0),
                                child: Image.asset(
                                  Images.userIcon,
                                  height: 20,
                                  width: 10,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.colorPrimary),
                                  borderRadius: BorderRadius.circular(16)),
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16)),
                              labelText: "Email",
                              labelStyle:
                                  TextStyle(color: AppColors.colorPrimary),
                              hintText: "Enter your email here"),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 25.0, left: 10, right: 10),
                        child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.colorPrimary,
                              foregroundColor: Colors.black,
                              minimumSize: const Size(double.infinity, 40),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(15.0)),
                            ),
                            onPressed: () {
                              if (emailcontroller.text.isEmpty) {
                                alertmsg('Fill required fields', context);
                              } else {
                                context
                                    .read<ForgetPasswordCubit>()
                                    .forgetLogin(emailcontroller.text);
                              }
                            },
                            child: const Text('SEND EMAIL',
                                style: TextStyle(fontWeight: FontWeight.bold))),
                      ),
                    ]),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  String? validateEmail(String? value) {
    const pattern = r"(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'"
        r'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-'
        r'\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*'
        r'[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4]'
        r'[0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9]'
        r'[0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\'
        r'x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])';
    final regex = RegExp(pattern);

    return value!.isNotEmpty && !regex.hasMatch(value)
        ? 'Enter a valid email address'
        : null;
  }
}
