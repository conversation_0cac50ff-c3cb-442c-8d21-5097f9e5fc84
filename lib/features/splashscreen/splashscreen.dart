import 'dart:async';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:legacy/common_widgets/notificationhandle.dart';
import 'package:legacy/features/channels/channels.dart';
import 'package:legacy/features/homepage/home.dart';
import 'package:legacy/features/loginpage/loginpage.dart';
import 'package:legacy/main.dart';
import 'package:legacy/utils/app_pref.dart';
import 'package:legacy/utils/app_styles.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:legacy/utils/models/withoutloginmodel.dart';

import '../../common_widgets/controllers/network_controller.dart';
import '../../common_widgets/tools.dart';

class Splash extends StatefulWidget {
  // ignore: prefer_typing_uninitialized_variables

  const Splash({super.key});

  @override
  State<Splash> createState() => _SplashState();
}

class _SplashState extends State<Splash> {
  var homeScreen;

  @override
  void initState() {
    print('risaj======1');
    super.initState();
    // appref();
    dev_id();
    network();
    // page();

    Timer(const Duration(seconds: 4), () {
      print('hi1122');
      print('${pageload}---------------111111111');
      // ignore: unrelated_type_equality_checks

      // if ((AppPref.getBool('loggedIn')) == true) {
      //   setState(() {

      //   });
      // } else {
      //   setState(() {

      //   });
      // }

// NotificationController.onActionReceivedMethod(ReceivedAction());

      moveToPage();
    });
  }

  moveToPage() {
    if (bgNotificationReceived.value = false) {
      Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (BuildContext context) => homeScreen),
          (Route<dynamic> route) => false);
    }
    if (bgNotificationReceived.value = true) {
      // bgNotificationReceived.value=false;
      Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (BuildContext context) => homeScreen),
          (Route<dynamic> route) => false);
    }
  }

  network() async {
    print("-------22222-----------${AppPref.getString('token')}");
    WithoutLoginModel without = await NetworkController().withoutLoggin();
    if (without.status == true) {
      setState(() {
        homeScreen = const MainHome();
      });
      AppPref.setBool('loggedIn', false);
      AppPref.setString('withoutlog', '1');
    } else {
      if (AppPref.getBool('loggedIn') == true) {
        setState(() {
          homeScreen = const MainHome();
        });
        AppPref.setString('withoutloginmsg', '0');
        AppPref.setString('withoutlog', '0');

        // AppPref.setString('withoutlogins', '1');
      } else {
        setState(() {
          homeScreen = const LoginPage();
        });
      }
    }
  }

  // page() {
  //   if (AppPref.getString('statuslogin') == 'true') {
  //         print('1111111------------------1');

  //     setState(() {
  //       homeScreen = const MainHome();

  //       AppPref.setBool('loggedIn', true);
  //       // homeScreen = const LoginPage();
  //     });
  //   } else if (AppPref.getString('statuslogin') == 'false' &&
  //       AppPref.getBool('loggedIn') == false) {
  //         print('1111111------------------2');
  //    setState(() {
  //       homeScreen = const LoginPage();
  //    });
  //   } else if (AppPref.getBool('loggedIn') == true) {
  //         print('1111111------------------3');

  //    setState(() {
  //       homeScreen = const MainHome();
  //    });
  //   } else {
  //         print('1111111------------------4');

  //       // AppPref.setBool('loggedIn', true);
  //     setState(() {
  //         homeScreen = const LoginPage();
  //     });

  //       // homeScreen = const MainHome();

  //   }
  // }

  @override
  Widget build(BuildContext context) {
    print('risaj======1');

    return Scaffold(
      backgroundColor: Colors.black,
      // appBar: AppBar(forceMaterialTransparency: true,
      //   toolbarHeight: 0,
      //   systemOverlayStyle: const SystemUiOverlayStyle(
      //     systemNavigationBarColor: Colors.black,
      //     statusBarColor: Colors.black,
      //     systemStatusBarContrastEnforced: true,
      //   ),
      // ),
      body: Container(
          height: MediaQuery.of(context).size.height,
          width: double.infinity,
          decoration: const BoxDecoration(
              image: DecorationImage(
                  image: AssetImage('assets/images/authloadimg.png'),
                  fit: BoxFit.fill)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Legacy Leaders',
                style: textstylesplash,
              ),
              gapH8,
              Text(
                'RECLAIMING MANHOOD',
                style: textstylesplash2,
              )
            ],
          )),
    );
  }
}
