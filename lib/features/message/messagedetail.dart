import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:legacy/features/message/cubit/cubit/messagedetails_cubit.dart';
import 'package:legacy/features/message/models/mesage.dart';
import 'package:legacy/utils/images.dart';
import 'package:legacy/utils/timeformat.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../common_widgets/formfield.dart';
import '../../common_widgets/tools.dart';
import '../../utils/app_pref.dart';
import '../../utils/appcolors.dart';
import '../../utils/gaps.dart';
import '../../utils/progress.dart';
import 'models/messagedetailmodel.dart';

class MessageDetailScreen extends StatefulWidget {
  const MessageDetailScreen({super.key, this.messageId});

  final messageId;
  // final Function onSend;

  @override
  State<MessageDetailScreen> createState() => _MessageScreenState();
}

class _MessageScreenState extends State<MessageDetailScreen> {
  ScrollController _scrollController = ScrollController();
  TextEditingController namecontroller = TextEditingController();

  TextEditingController emailcontroller = TextEditingController();
  TextEditingController commentcontroller = TextEditingController();

  ChatDetailModel? chatdetailscreen;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<MessagedetailsCubit>().chatDetailLoad(widget.messageId);
    // _scrollController.animateTo(
    //   0,
    //   curve: Curves.easeOut,
    //   duration: const Duration(milliseconds: 500),
    // );
  }

  @override
  Widget build(BuildContext context) {
    return AppLoader(
      child: BlocConsumer<MessagedetailsCubit, MessagedetailsState>(
          listener: (context, state) {
        if (state is Loaded) {
          print('5-------5${state.chatdetails}');
          setState(() {
            chatdetailscreen = state.chatdetails;
          });
          Timer(const Duration(milliseconds: 1000), () {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              curve: Curves.easeOut,
              duration: const Duration(milliseconds: 500),
            );
          });
        }
      }, builder: (context, state) {
        print('002200${chatdetailscreen?.data}');
        (state is Loading)
            ? context.loaderOverlay.show()
            : context.loaderOverlay.hide();
        if (state is Loaded) {
          return SizedBox(
            height: MediaQuery.of(context).size.height,
            width: double.infinity,
            child: Scaffold(
              resizeToAvoidBottomInset: true,
              appBar: AppBar(
                backgroundColor: AppColors.colorDarkYellow,
                title: Text(
                  '${chatdetailscreen?.data?[0].title}',
                  style: const TextStyle(color: Colors.black,fontSize: 20,fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis,
                ),
                centerTitle: true,
                leading: GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: SizedBox(
                        height: 10,
                        child: Image.asset('assets/images/backArrow.png')),
                  ),
                ),
              ),
              body: Container(
                height: MediaQuery.of(context).size.height - 56,
                child: Column(
                  children: [
                    Container(
                      height: 60,
                      color: AppColors.colorPrimary.withOpacity(.6),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                          // hhjjhjhjnkkkanjkxbchva https://www.dropbox.com
                          child: TextWithLinkChecker(textContent:chatdetailscreen?.data?[0].content ?? '',),
                          // child: Linkify(
                          //   overflow: TextOverflow.clip,
                           
                          //   onOpen: (link) async {
                          //     if (await canLaunchUrl(Uri.parse(link.url))) {
                          //       // await launchUrl(Uri.parse(link.url));
                          //       await launchUrl(Uri.parse(link.url),
                          //           mode: LaunchMode.externalApplication);
                          //     } else {
                          //       throw 'Could not launch $link';
                          //     }
                          //   },
                          //   text: chatdetailscreen?.data?[0].content ?? '',
                          //   style: const TextStyle(
                          //       overflow: TextOverflow.ellipsis,
                          //       color: Colors.black,
                          //       fontSize: 14,
                          //       fontWeight: FontWeight.bold),
                          //   linkStyle: const TextStyle(
                          //       color: Color.fromARGB(255, 26, 65, 96),
                          //       fontSize: 12),
                          // ),
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 9,
                      child: Container(
                        child: ListView.builder(
                          // shrinkWrap: true,
                          controller: _scrollController,
                          itemCount:
                              chatdetailscreen?.data?[0].comments?.length,
                          itemBuilder: (BuildContext context, index) {
                            // print(
                            //     '1111111111111${AppPref.getString('usr_id')}');
                            // print(
                            // '2222222222222${chatdetailscreen?.data?[0].comments?[index].userId}');
                            print(
                                'logout11111111111${AppPref.getString('withoutloginmsg')}');
                            print(
                                'logout11111111112${AppPref.getString('withoutlog')}');
                            print('2222222222222${AppPref.getString('usr')}');

                            if (chatdetailscreen
                                    ?.data?[0].comments?[index].userId ==
                                AppPref.getString('usr_id')) {
                              return Column(
                                children: [
                                  MessageItemDetail(
                                      date: timeChange(
                                          '${chatdetailscreen?.data?[0].comments?[index].date}'),
                                      messageText: chatdetailscreen?.data?[0]
                                              .comments?[index].text ??
                                          '4545',
                                      senderName: chatdetailscreen?.data?[0]
                                              .comments?[index].name ??
                                          '5454'),
                                ],
                              );
                            }
                            return MessageItemchat(
                                date: timeChange(
                                    '${chatdetailscreen?.data?[0].comments?[index].date}'),
                                messageText: chatdetailscreen
                                        ?.data?[0].comments?[index].text ??
                                    '7474',
                                senderName: chatdetailscreen
                                        ?.data?[0].comments?[index].name ??
                                    '7474');
                          },
                        ),
                      ),
                    ),
                    // const Spacer(),
                    Container(
                      height: 60,
                      width: double.infinity,
                      color: const Color(0XFFE6E6E6),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                            child: AppPref.getString('withoutloginmsg') ==
                                        '1' ||
                                    AppPref.getBool('loggedIn') == true
                                ? TextFormField(
                                    textInputAction: up(),
                                    validator: validateEmail,
                                    controller: commentcontroller,
                                    decoration: InputDecoration(
                                        border: InputBorder.none,
                                        suffixIcon: GestureDetector(
                                            onTap: () {
                                              if (commentcontroller
                                                  .text.isEmpty) {
                                                alertmsg(' Enter comment first',
                                                    context);
                                              } else {
                                                print(
                                                    '-------1122--------${AppPref.getString('usr')}');
                                                print(
                                                    '34343434343-----------${AppPref.getString('email_id')}');
                                                context.read<MessagedetailsCubit>().sendmessage(
                                                    AppPref.getString(
                                                                'withoutloginmsg') ==
                                                            '1'
                                                        ? widget.messageId
                                                        : widget.messageId,
                                                    AppPref.getString(
                                                                'withoutloginmsg') ==
                                                            '1'
                                                        ? AppPref.getString(
                                                                'withoutloginemail')
                                                            .toString()
                                                        : AppPref.getString(
                                                                'email_id')
                                                            .toString(),
                                                    AppPref.getString(
                                                                    'withoutloginmsg') ==
                                                                '1' &&
                                                            AppPref.getString(
                                                                    'withoutlog') ==
                                                                '1'
                                                        ? AppPref.getString(
                                                                'withoutloginname')
                                                            .toString()
                                                        : AppPref.getString(
                                                                'usr')
                                                            .toString(),
                                                    commentcontroller.text,
                                                    '');
                                                // commentcontroller.clear();
                                                var localValues = Comment(
                                                  id: '',
                                                  userId: AppPref.getString(
                                                              'withoutloginmsg') ==
                                                          '1'
                                                      ? AppPref.getString(
                                                          'usr_id')
                                                      : AppPref.getString(
                                                          'usr_id'),
                                                  name: AppPref.getString(
                                                                  'withoutloginmsg') ==
                                                              '1' &&
                                                          AppPref.getString(
                                                                  'withoutlog') ==
                                                              '1'
                                                      ? AppPref.getString(
                                                              'withoutloginname')
                                                          .toString()
                                                      : AppPref.getString('usr')
                                                          .toString(),
                                                  text: commentcontroller.text,
                                                  date: DateTime.now(),
                                                );

                                                setState(() {
                                                  chatdetailscreen
                                                      ?.data?[0].comments
                                                      ?.add(localValues);
                                                  // context
                                                  // .read<MessagedetailsCubit>()
                                                  // .chatDetailLoad(widget.messageId);
                                                });
                                                Timer(
                                                    const Duration(
                                                        milliseconds: 100), () {
                                                  _scrollController.animateTo(
                                                    _scrollController.position
                                                        .maxScrollExtent,
                                                    curve: Curves.easeOut,
                                                    duration: const Duration(
                                                        milliseconds: 500),
                                                  );
                                                });
                                                commentcontroller.clear();
                                              }
                                            },
                                            child: SizedBox(
                                                height: 15,
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.all(6.0),
                                                  child: Image.asset(
                                                    Images.messagesend,
                                                  ),
                                                ))),
                                        hintStyle: const TextStyle(
                                            color: Colors.black, fontSize: 15),
                                        contentPadding:
                                            EdgeInsets.only(left: 32, top: 10),
                                        // prefix: SizedBox(width: 10),

                                        // labelText: "Search Audios",
                                        // labelStyle: TextStyle(color: AppColors.colorPrimary),
                                        hintText: "Comments"),
                                  )
                                : GestureDetector(
                                    child: Container(
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(15),
                                          color: AppColors.colorDarkYellow),
                                      height: 50,
                                      width: double.infinity,
                                      child: const Center(
                                          child: Text(
                                        'Start',
                                        style: TextStyle(
                                            fontSize: 17,
                                            fontWeight: FontWeight.bold),
                                      )),
                                    ),
                                    onTap: () {
                                      showModalBottomSheet(
                                          useRootNavigator: false,
                                          isScrollControlled: true,
                                          context: context,
                                          builder: (context) {
                                            return Padding(
                                              padding: EdgeInsets.only(
                                                  top: 16,
                                                  left: 10,
                                                  right: 10,
                                                  bottom: MediaQuery.of(context)
                                                      .viewInsets
                                                      .bottom),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: <Widget>[
                                                  gapH12,
                                                  TextFormField(
                                                    controller: namecontroller,
                                                    validator: validateEmail,
                                                    decoration: InputDecoration(
                                                      focusedBorder: OutlineInputBorder(
                                                          borderSide: BorderSide(
                                                              color: AppColors
                                                                  .colorPrimary),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      12)),
                                                      border:
                                                          OutlineInputBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          12)),
                                                      labelText: "Name",
                                                      contentPadding:
                                                          EdgeInsets.only(
                                                              left: 15),
                                                      labelStyle: TextStyle(
                                                          color: AppColors
                                                              .colorPrimary),
                                                    ),
                                                  ),
                                                  gapH12,
                                                  TextFormField(
                                                    controller: emailcontroller,
                                                    validator: validateEmail,
                                                    decoration: InputDecoration(
                                                      focusedBorder: OutlineInputBorder(
                                                          borderSide: BorderSide(
                                                              color: AppColors
                                                                  .colorPrimary),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      12)),
                                                      border:
                                                          OutlineInputBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          12)),
                                                      labelText: "Email",
                                                      contentPadding:
                                                          EdgeInsets.only(
                                                              left: 15),
                                                      labelStyle: TextStyle(
                                                          color: AppColors
                                                              .colorPrimary),
                                                    ),
                                                  ),
                                                  gapH12,
                                                  SizedBox(
                                                    height: 80,
                                                    child: TextFormField(
                                                      style: TextStyle(
                                                          color: Colors.black),
                                                      maxLines: 3,
                                                      controller:
                                                          commentcontroller,
                                                      decoration: InputDecoration(
                                                          focusedBorder: OutlineInputBorder(
                                                              borderSide: BorderSide(
                                                                  color: AppColors
                                                                      .colorPrimary),
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                      12)),
                                                          border: OutlineInputBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          12)),
                                                          labelText: "Comment",
                                                          contentPadding:
                                                              EdgeInsets.only(
                                                                  top: 10,
                                                                  bottom: 10,
                                                                  left: 15),
                                                          labelStyle: TextStyle(
                                                              color: AppColors
                                                                  .colorPrimary),
                                                          hintText:
                                                              "Enter your comments here"),
                                                    ),
                                                  ),
                                                  gapH12,
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceEvenly,
                                                    children: [
                                                      ElevatedButton(
                                                          style: ElevatedButton
                                                              .styleFrom(
                                                                  side:
                                                                      const BorderSide(
                                                                    color: Colors
                                                                        .red,
                                                                  ),
                                                                  padding: const EdgeInsets
                                                                          .symmetric(
                                                                      horizontal:
                                                                          60.0,
                                                                      vertical:
                                                                          15.0),
                                                                  shape: RoundedRectangleBorder(
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              10.0)),
                                                                  backgroundColor:
                                                                      Colors
                                                                          .white),
                                                          onPressed: () {
                                                            Navigator.pop(
                                                                context);
                                                          },
                                                          child: const Text(
                                                              'Cancel',
                                                              style: TextStyle(
                                                                  fontSize: 17,
                                                                  color: Colors
                                                                      .red,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold))),
                                                      ElevatedButton(
                                                          onPressed: () {
                                                            if ((namecontroller
                                                                    .text
                                                                    .isEmpty) ||
                                                                (commentcontroller
                                                                    .text
                                                                    .isEmpty) ||
                                                                (emailcontroller
                                                                    .text
                                                                    .isEmpty)) {
                                                              alertmsg(
                                                                  'Fill required fields',
                                                                  context);
                                                            } else {
                                                              context.read<MessagedetailsCubit>().sendmessage(
                                                                  widget
                                                                      .messageId,
                                                                  '',
                                                                  namecontroller
                                                                      .text,
                                                                  commentcontroller
                                                                      .text,
                                                                  AppPref.getString(
                                                                          'usr_id')
                                                                      .toString());
                                                              var localValues1 = Comment(
                                                                  name:
                                                                      namecontroller
                                                                          .text,
                                                                  text:
                                                                      commentcontroller
                                                                          .text,
                                                                  email:
                                                                      emailcontroller
                                                                          .text,
                                                                  date: DateTime
                                                                      .now());

                                                              setState(() {
                                                                chatdetailscreen
                                                                    ?.data?[0]
                                                                    .comments
                                                                    ?.add(
                                                                        localValues1);
                                                                AppPref.setString(
                                                                    'withoutloginmsg',
                                                                    '1');
                                                                AppPref.setString(
                                                                    'withoutlog',
                                                                    '1');
                                                                AppPref.setString(
                                                                    'withoutloginname',
                                                                    namecontroller
                                                                        .text);
                                                                AppPref.setString(
                                                                    'withoutloginemail',
                                                                    emailcontroller
                                                                        .text);

                                                                // context
                                                                // .read<MessagedetailsCubit>()
                                                                // .chatDetailLoad(widget.messageId);
                                                              });
                                                              Navigator.pop(
                                                                  context);
                                                              commentcontroller
                                                                  .clear();
                                                            }
                                                          },
                                                          style: ElevatedButton.styleFrom(
                                                              padding: const EdgeInsets
                                                                      .symmetric(
                                                                  horizontal:
                                                                      60.0,
                                                                  vertical:
                                                                      15.0),
                                                              shape: RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                          10.0)),
                                                              backgroundColor:
                                                                  AppColors
                                                                      .colorDarkYellow),
                                                          child: const Text(
                                                              'Send',
                                                              style: TextStyle(
                                                                  fontSize: 17,
                                                                  color: Colors
                                                                      .black,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold)))
                                                    ],
                                                  ),
                                                  gapH12
                                                ],
                                              ),
                                            );
                                          });
                                    },
                                  )),
                      ),
                    ),
                    // gapH12
                  ],
                ),
              ),
            ),
          );
        }
        return Scaffold(
          appBar: AppBar(
            backgroundColor: AppColors.colorDarkYellow,
            leading: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: SizedBox(
                    height: 10,
                    child: Image.asset('assets/images/backArrow.png')),
              ),
            ),
          ),
          body: Container(
              child: Center(
                child: Text(
                  'No messages to show',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
              color: Colors.white,
              height: double.infinity,
              width: double.infinity),
        );
      }),
    );
  }

  up() {
    Timer(const Duration(milliseconds: 100), () {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        curve: Curves.easeOut,
        duration: const Duration(milliseconds: 500),
      );
    });
  }
}

String? validateEmail(String? value) {
  const pattern = r"(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'"
      r'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-'
      r'\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*'
      r'[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4]'
      r'[0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9]'
      r'[0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\'
      r'x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])';
  final regex = RegExp(pattern);

  return value!.isNotEmpty && !regex.hasMatch(value)
      ? 'Enter a valid email address'
      : null;
}

// return chatdetailscreen?.data?[0].comments?.length==6 ? MessageItemDetail(
//     date: chatdetailscreen?.data?[0].comments?[index].date
//             .toString() ??
//         '',
//     messageText: chatdetailscreen
//             ?.data?[0].comments?[index].text ??
//         '',
//     senderName: chatdetailscreen
//             ?.data?[0].comments?[index].name ??
// ''):

class LinkCheckerWidget extends StatelessWidget {
  final String textContent;

  LinkCheckerWidget({required this.textContent});

  @override
  Widget build(BuildContext context) {
    bool hasLink = _checkForLink(textContent);

    return hasLink
        ? Text('true')
        : Text(
            'false', );
  }

  bool _checkForLink(String text) {
    final linkRegex =
        RegExp(r'http(s)?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+');
    return linkRegex.hasMatch(text);
  }
}
class LinkCheckerWidgetreturn extends StatelessWidget {
  final String textContent;

  LinkCheckerWidgetreturn({required this.textContent});

  @override
  Widget build(BuildContext context) {
    var link;
    bool hasLink = _checkForLink(textContent);
    if(hasLink){
      final linkRegex = RegExp(r'http(s)?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+');
      final linkMatch = linkRegex.firstMatch(textContent);

      if (linkMatch != null) {
        // Extract the link
        final link = linkMatch.group(0);}
    }

    return link;
  }
  bool _checkForLink(String text) {
    final linkRegex =
        RegExp(r'http(s)?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+');
    return linkRegex.hasMatch(text);
  }}
class TextLengthChecker extends StatelessWidget {
  final String textContent;

  TextLengthChecker({required this.textContent});

  @override
  Widget build(BuildContext context) {
    return textContent.length > 100
        ? Text(
            'true',
            style: TextStyle(color: Colors.red),
          )
        : Text(
            'false',
            style: TextStyle(color: Colors.green),
          );
  }
}
class TextWithLinkChecker extends StatelessWidget {
  final String textContent;

  TextWithLinkChecker({required this.textContent});

  @override
  Widget build(BuildContext context) {
    print('object------------$textContent');
    // Check if text content has more than 100 letters
    if (textContent.length > 100) {
      // Find the first link in the paragraph
      final linkRegex = RegExp(r'http(s)?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+');
      final linkMatch = linkRegex.firstMatch(textContent);

      if (linkMatch != null) {
        print('noooooooo');
        // Extract the link
        final link = linkMatch.group(0);

        // Display the first link if it exists
        return GestureDetector(
          onTap: () {
            // Open the link when tapped
            // Replace 'launchURL' with your actual URL opening function
            launchURL('$link');
          },
          child: Text(
            '$link', // Show only the first 100 characters
            style: TextStyle(
              color: Colors.blue, // Assuming links are blue
              decoration: TextDecoration.underline,overflow: TextOverflow.clip
            ),
          ),
        );
      }else{
        print('noooooooo1');

        return  Center(child: Text(textContent.substring(0, 100)));
      }
    }
    if(textContent!=null){
    print('object---------2222---$textContent');

      return Linkify(textAlign: TextAlign.center,
                            overflow: TextOverflow.clip,
                           
                            onOpen: (link) async {
                             print('launch-----1');

                              if (await canLaunchUrl(Uri.parse(link.url))) {
                                // await launchUrl(Uri.parse(link.url));
                                await launchUrl(Uri.parse(link.url),
                                    mode: LaunchMode.externalApplication);
                              } else {
                                throw 'Could not launch $link';
                              }
                            },
                            text:textContent,
                            style: const TextStyle(
                                overflow: TextOverflow.ellipsis,
                                color: Colors.black,
                                fontSize: 14,
                                fontWeight: FontWeight.bold),
                            linkStyle: const TextStyle(
                                color: Colors.blue, // Assuming links are blue
              decoration: TextDecoration.underline,
                                fontSize: 12),
                          );
    }else{
      return Text('data');
    }

    // If there are no links or text is within 100 characters, display the text normally
    // return Text(textContent);
  }}

  Future<void> launchURL(String url) async {
    print('launch-----');
    print('launch-----$url');
     if (await canLaunchUrl((Uri.parse(url)))) {
      await launchUrl((Uri.parse(url)),
      mode:LaunchMode.externalApplication );
    } else {
      throw 'Could not launch $url';
    }
  }
    // Linkify(
    //                         overflow: TextOverflow.clip,
                           
    //                         onOpen: (url) async {
    //                          print('launch-----1');

                              // if (await canLaunchUrl(Uri.parse(url.url))) {
                                // await launchUrl(Uri.parse(link.url));
                                // await launchUrl(Uri.parse(url.url),
                          //           mode: LaunchMode.externalApplication);
                          //     } else {
                          //       throw 'Could not launch $url';
                          //     }
                          //   },
                          //   text:url,
                          //   style: const TextStyle(
                          //       overflow: TextOverflow.ellipsis,
                          //       color: Colors.black,
                          //       fontSize: 14,
                          //       fontWeight: FontWeight.bold),
                          //   linkStyle: const TextStyle(
                          //       color: Color.fromARGB(255, 26, 65, 96),
                          //       fontSize: 12),
                          // );
  
  // }
