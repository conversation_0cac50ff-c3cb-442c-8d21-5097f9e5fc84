// To parse this JSON data, do
//
//     final messageDetailModel = messageDetailModelFromJson(jsonString);

import 'dart:convert';

ChatDetailModel messageDetailModelFromJson(String str) =>
    ChatDetailModel.fromJson(json.decode(str));

String messageDetailModelToJson(ChatDetailModel data) =>
    json.encode(data.toJson());

class ChatDetailModel {
  bool? status;
  String? message;
  List<Datum>? data;

  ChatDetailModel({
    this.status,
    this.message,
    this.data,
  });

  factory ChatDetailModel.fromJson(Map<String, dynamic> json) =>
      ChatDetailModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  String? id;
  String? title;
  String? content;
  DateTime? date;
  String? churchId;
  List<Comment>? comments;
  DateTime? createdAt;
  DateTime? updatedAt;

  Datum({
    this.id,
    this.title,
    this.content,
    this.date,
    this.churchId,
    this.comments,
    this.createdAt,
    this.updatedAt,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["_id"],
        title: json["title"],
        content: json["content"],
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
        churchId: json["churchId"],
        comments: json["comments"] == null
            ? []
            : List<Comment>.from(
                json["comments"]!.map((x) => Comment.fromJson(x))),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "title": title,
        "content": content,
        "date": date?.toIso8601String(),
        "churchId": churchId,
        "comments": comments == null
            ? []
            : List<dynamic>.from(comments!.map((x) => x.toJson())),
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
      };
}

class Comment {
  String? userId;
  String? text;
  String? name;
  String? email;
  DateTime? date;
  String? id;

  Comment({
    this.userId,
    this.text,
    this.name,
    this.email,
    this.date,
    this.id,
  });

  factory Comment.fromJson(Map<String, dynamic> json) => Comment(
        userId: json["userId"],
        text: json["text"],
        name: json["name"],
        email: json["email"],
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "text": text,
        "name": name,
        "email": email,
        "date": date?.toIso8601String(),
        "_id": id,
      };
}
