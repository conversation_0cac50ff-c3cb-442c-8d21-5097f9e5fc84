import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:legacy/features/message/cubit/messageboard_cubit.dart';
import 'package:legacy/features/message/models/chatmodel.dart';
import 'package:legacy/utils/appcolors.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:legacy/utils/images.dart';
import 'package:linkify/linkify.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:url_launcher/url_launcher.dart';

class MessageItem extends StatefulWidget {
  final String senderName;
  final String messageText;
  final String date;
  const MessageItem({
    super.key,
    required this.senderName,
    required this.messageText,
    required this.date,
  });

  @override
  _MessageItemState createState() => _MessageItemState();
}

class _MessageItemState extends State<MessageItem> {
  @override
  // void initState() {
  //   // TODO: implement initState
  //   super.initState();
  //   context.read<MessageboardCubit>().chatLoad();
  // }
  @override
  Widget build(BuildContext context) {
    // ChatModel? chats;
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            margin: const EdgeInsets.only(right: 12.0),
            child: const CircleAvatar(
                radius: 22,
                backgroundColor: Colors.white,
                child: Center(
                    child: Icon(
                  Icons.account_circle,
                  color: Color(0XFFB9850A),
                  size: 45,
                ))),
          ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: const Color(0XFFE6E6E6),
                borderRadius: BorderRadius.circular(20.0),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(widget.senderName,
                            style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: Colors.black)),
                      ),
                      gapW16,
                      // const SizedBox(width: 4.0),
                      // const Text('•', style: TextStyle(color: Colors.white70)),
                      // const SizedBox(width: 4.0),
                      Expanded(
                          flex: 1,
                          child: Text(widget.date,
                              style: const TextStyle(
                                  color: Colors.black54, fontSize: 12))),
                    ],
                  ),
                  gapH24,
                  Text(
                    widget.messageText,
                    style: const TextStyle(color: Colors.black, fontSize: 14),
                  ),
                  gapH4
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class MessageItemchat extends StatefulWidget {
  final String senderName;
  final String messageText;
  final String date;
  const MessageItemchat({
    super.key,
    required this.senderName,
    required this.messageText,
    required this.date,
  });

  @override
  _MessageItemchatState createState() => _MessageItemchatState();
}

class _MessageItemchatState extends State<MessageItemchat> {
  @override
  // void initState() {
  //   // TODO: implement initState
  //   super.initState();
  //   context.read<MessageboardCubit>().chatLoad();
  // }
  @override
  Widget build(BuildContext context) {
    // ChatModel? chats;
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(right: 12.0),
            child: const CircleAvatar(
                radius: 22,
                backgroundColor: Colors.white,
                child: Center(
                    child: Icon(
                  Icons.account_circle,
                  color: Color(0XFFB9850A),
                  size: 45,
                ))),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                padding: const EdgeInsets.only(
                    top: 12, right: 12, bottom: 12, left: 30),
                decoration: const BoxDecoration(
                    color: Color(0XFFE6E6E6),
                    borderRadius: BorderRadius.only(
                        topRight: Radius.circular(50),
                        bottomLeft: Radius.circular(50),
                        bottomRight: Radius.circular(50))),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(widget.senderName,
                        style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.black)),
                    // const SizedBox(width: 4.0),
                    // const Text('•', style: TextStyle(color: Colors.white70)),
                    // const SizedBox(width: 4.0),

                    gapH8,
                    Linkify(
                      overflow: TextOverflow.visible,
                      onOpen: (link) async {
                        if (await canLaunchUrl(Uri.parse(link.url))) {
                          // await launchUrl(Uri.parse(link.url));
                          await launchUrl(Uri.parse(link.url),
                              mode: LaunchMode.externalApplication);
                        } else {
                          throw 'Could not launch $link';
                        }
                      },
                      text: widget.messageText,
                      style: const TextStyle(
                        overflow: TextOverflow.ellipsis,
                        color: Colors.black,
                        fontSize: 14,
                      ),
                      linkStyle: const TextStyle(
                          color: Color.fromARGB(255, 26, 65, 96), fontSize: 15),
                    ),
                    // Linkify(
                    //   onOpen: (url) => print("Clicked $url!"),
                    //   text: "Made by https://cretezy.com",
                    // ),
                    // linkify(widget.messageText.toString());

                    // Text(
                    //   widget.messageText,
                    //   style: const TextStyle(color: Colors.blue,),
                    // ),
                    gapH8,
                    Text(widget.date,
                        style: const TextStyle(
                            color: Colors.black54, fontSize: 12)),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class MessageItemDetail extends StatefulWidget {
  final String senderName;
  final String messageText;
  final String date;
  const MessageItemDetail({
    super.key,
    required this.senderName,
    required this.messageText,
    required this.date,
  });

  @override
  State<MessageItemDetail> createState() => _MessageItemDetailState();
}

class _MessageItemDetailState extends State<MessageItemDetail> {
  @override
  Widget build(BuildContext context) {
    return
        // ChatModel? chats;

        Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                padding: const EdgeInsets.only(
                    top: 12, right: 12, bottom: 12, left: 30),
                decoration: BoxDecoration(
                    color: AppColors.colorDarkYellow,
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(50),
                        bottomLeft: Radius.circular(50),
                        bottomRight: Radius.circular(50))),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(widget.senderName,
                        style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.white)),
                    // const SizedBox(width: 4.0),
                    // const Text('•', style: TextStyle(color: Colors.white70)),
                    // const SizedBox(width: 4.0),

                    gapH8,

                    Linkify(
                      overflow: TextOverflow.visible,
                      onOpen: (link) async {
                        if (await canLaunchUrl(Uri.parse(link.url))) {
                          // await launchUrl(Uri.parse(link.url));
                          await launchUrl(Uri.parse(link.url),
                              mode: LaunchMode.externalApplication);
                        } else {
                          throw 'Could not launch $link';
                        }
                      },
                      text: widget.messageText,
                      style: const TextStyle(
                        overflow: TextOverflow.ellipsis,
                        color: Colors.white,
                        fontSize: 14,
                      ),
                      linkStyle: const TextStyle(
                          color: Color.fromARGB(255, 26, 65, 96), fontSize: 15),
                    ),
                    // Linkify(
                    //   onOpen: (url) => print("Clicked $url!"),
                    //   text: "Made by https://cretezy.com",
                    // ),
                    // linkify(widget.messageText.toString());

                    // Text(
                    //   widget.messageText,
                    //   style: const TextStyle(color: Colors.blue,),
                    // ),
                    gapH8,
                    Text(widget.date,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 12)),
                  ],
                ),
              ),
            ),
          ),
          Container(
            margin: const EdgeInsets.only(right: 12.0),
            child: const CircleAvatar(
                radius: 22,
                backgroundColor: Colors.white,
                child: Center(
                    child: Icon(
                  Icons.account_circle_sharp,
                  color: Color.fromARGB(208, 225, 32, 32),
                  size: 48,
                ))),
          ),
        ],
      ),
    );
  }
}
