import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/features/homepage/home.dart';
import 'package:legacy/features/message/models/mesage.dart';
import 'package:legacy/features/message/messagedetail.dart';
import 'package:legacy/main.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:legacy/utils/images.dart';
import 'package:legacy/utils/timeformat.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import '../../utils/appcolors.dart';
import '../../utils/progress.dart';
import 'cubit/messageboard_cubit.dart';
import 'models/chatmodel.dart';

class MessageScreen extends StatefulWidget {
  const MessageScreen({super.key});

  @override
  State<MessageScreen> createState() => _MessageScreenState();
}

class _MessageScreenState extends State<MessageScreen> {
  ChatModel? chatscreen;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<MessageboardCubit>().chatLoad();
  }

  @override
  Widget build(BuildContext context) {
    return AppLoader(
        child: BlocConsumer<MessageboardCubit, MessageboardState>(
            listener: (context, state) {
      if (state is Loaded) {
        print('5-------5${state.chat}');
        setState(() {
          chatscreen = state.chat;
        });
      }
      // TODO: implement listener
    }, builder: (context, state) {
      // String date = chatscreen. ??
      '2023-04-17T16:51:51.897Z';

      (state is Loading)
          ? context.loaderOverlay.show()
          : context.loaderOverlay.hide();
      return Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.colorDarkYellow,
          title: const Text(
            'Message Board',
            style: TextStyle(color: Colors.black,fontSize: 18,fontWeight: FontWeight.bold),
          ),
          centerTitle: true,
          leading: GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
              // pushNewScreen(
              //   context,
              //   screen: MainHome(),
              //   withNavBar: true, // OPTIONAL VALUE. True by default.
              //   pageTransitionAnimation: PageTransitionAnimation.cupertino,
              // );
              // controller.jumpToTab(1);
              // Navigator.pushReplacement(
              //     context,
              //     MaterialPageRoute(
              //       builder: (context) => HomePage(),
              //     ));
            },
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: SizedBox(
                  height: 10,
                  child: Image.asset('assets/images/backArrow.png')),
            ),
          ),
        ),
        body: chatscreen?.data?[0].date != null
            ? ListView.builder(
                itemCount: chatscreen?.data?.length,
                itemBuilder: (BuildContext context, index) {
                  return GestureDetector(
                      onTap: () {
                        print(
                            '31123545-----------------${chatscreen?.data?[index].id}');
                        pushNewScreen(
                          context,
                          screen: MessageDetailScreen(
                            messageId: chatscreen?.data?[index].id,
                          ),
                          withNavBar: false, // OPTIONAL VALUE. True by default.
                          pageTransitionAnimation:
                              PageTransitionAnimation.cupertino,
                        );
                      },
                      child: MessageItem(
                        date: formatDateTimeMessage(
                            "${chatscreen?.data?[index].date}"),
                        messageText:
                            chatscreen?.data?[index].content.toString() ?? '',
                        senderName:
                            chatscreen?.data?[index].title.toString() ?? '',
                      ));
                })
            : Container(
                child: Center(
                  child: Text(
                    'No messages to show',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                color: Colors.white,
                height: double.infinity,
                width: double.infinity),
      );
    }));
  }
}
