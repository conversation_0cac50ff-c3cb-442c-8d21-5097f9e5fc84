import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../../common_widgets/controllers/network_controller.dart';
import '../../../../utils/app_pref.dart';
import '../../models/chatmodel.dart';
import '../../models/messagedetailmodel.dart';

part 'messagedetails_state.dart';

class MessagedetailsCubit extends Cubit<MessagedetailsState> {
  MessagedetailsCubit() : super(MessagedetailsInitial());
  Future<void> chatDetailLoad(String messageId) async {
    emit(Loading());
    String? token = AppPref.getString('token');
    // String? cat_id='626bff2f204c55339bbbb2f5';
    print('qwer$token');
    print('m-m-m-m-${messageId}');

    emit(Loading());
    try {
      ChatDetailModel body =
          await NetworkController().chatdetailscreen('$token', messageId);

// alertmsg('${AppPref.getString('msg')}');
      print('2-2-2-2-2');
      print('000000--------${body.data}');

      emit(Loaded(chatdetails: body));
    } catch (e) {
      emit(Message1(e.toString()));
      print('4444444${e.toString()}');
    }
  }

  Future<void> sendmessage(String messageId, String email, String name,
      String text, String userId) async {
    // emit(Loading());
    String? token = AppPref.getString('token');
    String? userId = AppPref.getString('usr_id');

    // String? cat_id='626bff2f204c55339bbbb2f5';
    print('qwer$token');
    var params = {'email': email, 'name': name, 'text': text, 'userId': userId};
    print(
        '------------------------------params-------------------------$params');

    // emit(Loading());
    try {
      ChatDetailModel body =
          await NetworkController().sendmsg('$token', messageId, params);
// alertmsg('${AppPref.getString('msg')}');
      print('2-2-2-$params');
      // print('000000--------${body.data}');
      // ChatDetailModel body =
      // await NetworkController().chatdetailscreen('$token', messageId);

// alertmsg('${AppPref.getString('msg')}');
      print('2-2-2-2-2');
      print('000000--------${body.data}');
    } catch (e) {
      // emit(Message1(e.toString()));
      print('44444441111${e.toString()}');
    }
  }
}
