part of 'messagedetails_cubit.dart';

@immutable
abstract class MessagedetailsState {}

class MessagedetailsInitial extends MessagedetailsState {}

class Loading extends MessagedetailsState {
  Loading();
}

class Loaded extends MessagedetailsState {
  final ChatDetailModel? chatdetails;

  Loaded({this.chatdetails});

  @override
  List<Object> get props => [];
}

class Message1 extends MessagedetailsState {
  final String message;

  Message1(this.message);

  @override
  List<Object> get props => [];
}
