import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../common_widgets/controllers/network_controller.dart';
import '../../../utils/app_pref.dart';
import '../models/chatmodel.dart';

part 'messageboard_state.dart';

class MessageboardCubit extends Cubit<MessageboardState> {
  MessageboardCubit() : super(MessageboardInitial());
  Future<void> chatLoad() async {
    emit(Loading());
    String? token = AppPref.getString('token');
    // String? cat_id='626bff2f204c55339bbbb2f5';
    print('qwer$token');

    emit(Loading());
    try {
      ChatModel body = await NetworkController().chatscreen('$token');

// alertmsg('${AppPref.getString('msg')}');
      print('2-2-2-2-2');
      print('000000--------${body.data}');

      emit(Loaded(chat: body));
    } catch (e) {
      emit(Message1(e.toString()));
      print('4444444${e.toString()}');
    }
  }
}
