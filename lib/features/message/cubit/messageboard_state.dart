part of 'messageboard_cubit.dart';

@immutable
abstract class MessageboardState {}

class MessageboardInitial extends MessageboardState {}

class Loading extends MessageboardState {
  Loading();
}

class Loaded extends MessageboardState {
  final ChatModel? chat;

  Loaded({this.chat});

  @override
  List<Object> get props => [];
}

class Message1 extends MessageboardState {
  final String message;

  Message1(this.message);

  @override
  List<Object> get props => [];
}
