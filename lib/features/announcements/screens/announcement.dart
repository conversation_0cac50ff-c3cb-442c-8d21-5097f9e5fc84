import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/common_widgets/sidedrawer/side_drawer.dart';
import 'package:legacy/features/announcements/screens/announcementdetail.dart';
import 'package:legacy/features/announcements/cubit/announcements_cubit.dart';
import 'package:legacy/utils/app_styles.dart';
import 'package:legacy/utils/appcolors.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:legacy/utils/images.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import '../../../main.dart';
import '../../../utils/progress.dart';
import '../../../utils/timeformat.dart';
import '../models/announcementmodel.dart';

class Announcements extends StatefulWidget {
  const Announcements({super.key});

  @override
  State<Announcements> createState() => _AnnouncementsState();
}

class _AnnouncementsState extends State<Announcements> {
  List<Datum> announcements = [];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<AnnouncementsCubit>().announcementlistLoad();
    print('risaj------------');
    // controller.jumpToTab(3);
  }

  final GlobalKey<ScaffoldState> _key = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return AppLoader(
        child: BlocConsumer<AnnouncementsCubit, AnnouncementsState>(
            listener: (context, state) {
      if (state is Loaded) {
        print('55555555${state.announcementlistdetails!.data}');
        setState(() {
          announcements = state.announcementlistdetails?.data as List<Datum>;
        });
      }
      // TODO: implement listener
    }, builder: (context, state) {
      (state is Loading)
          ? context.loaderOverlay.show()
          : context.loaderOverlay.hide();
      return Scaffold(
        drawerEnableOpenDragGesture: false,
        // drawer: const DrawerWidget(),
        // key: _key,
        appBar: AppBar(
          systemOverlayStyle:
              const SystemUiOverlayStyle(statusBarColor: Colors.black),
          leading: IconButton(
            icon: Image.asset(
              Images.menuBarIcon,
              scale: 2.5,
            ),
            onPressed: () {
              pushNewScreen(
                context,
                screen: DrawerWidget(),
                withNavBar: false, // OPTIONAL VALUE. True by default.
                pageTransitionAnimation: PageTransitionAnimation.slideRight,
              );
            },
          ),
          centerTitle: true,
          toolbarHeight: 80,
          title: SizedBox(
              height: 70,
              child: Image.asset(
                'assets/images/legacyleadersupdatelogo.png',
              )),
          backgroundColor: Colors.black,
        ),
        body: SizedBox(
          height: MediaQuery.of(context).size.height,
          child: Column(
            children: [
              Container(
                  height: 60,
                  color: const Color.fromARGB(255, 0, 0, 0),
                  width: double.infinity,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 15, vertical: 15),
                            child: SizedBox(
                                height: 22,
                                child: Image.asset(Images.announcementIcon)),
                          ),
                          Text('ANNOUNCEMENTS', style: textstyleheading),
                        ],
                      ),
                    ],
                  )),
              gapH16,
              Expanded(
                child: ListView.builder(
                  // separatorBuilder: (BuildContext context, index) {
                  //   return const Divider(
                  //     thickness: 1,
                  //     height: 1,
                  //     color: Colors.black,
                  //   );
                  // },
                  itemBuilder: (BuildContext context, index) {
                    return GestureDetector(
                      onTap: () => pushNewScreen(
                        context,
                        screen: AnnouncementDetail(
                          notificationId: announcements[index].id,
                        ),
                        withNavBar: false, // OPTIONAL VALUE. True by default.
                        pageTransitionAnimation:
                            PageTransitionAnimation.cupertino,
                      ).then((value) => context
                          .read<AnnouncementsCubit>()
                          .announcementlistLoad()),
                      child: Container(
                        color: Colors.transparent,
                        child: Column(
                          children: [
                            Row(
                              children: [
                                gapW8,
                                Container(
                                    height: 80,
                                    width: 80,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        border: Border.all(),
                                        color: Colors.white,
                                        image: DecorationImage(
                                          image: AssetImage(Images.bg),
                                          fit: BoxFit.contain,
                                        )),
                                    child: Center(
                                        child: Stack(
                                      children: [
                                        SizedBox(
                                          height: 85,
                                          width: 85,
                                          child: announcements[index]
                                                      .markAsRead !=
                                                  true
                                              ? const Align(
                                                  alignment: Alignment.topRight,
                                                  child: Row(
                                                    children: [
                                                      Spacer(),
                                                      CircleAvatar(
                                                        backgroundColor:
                                                            Colors.red,
                                                        radius: 3,
                                                      )
                                                    ],
                                                  ),
                                                )
                                              : Text(''),
                                        )
                                      ],
                                    ))),
                                gapW24,
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        announcements[index].title.toString(),
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 14),
                                      ),
                                      gapH8,
                                      Text(announcements[index]
                                          .content
                                          .toString()),
                                      gapH8,
                                      Row(
                                        children: [
                                          Text(
                                            timeAgo(announcements[index]
                                                    .createdAt ??
                                                DateTime.now()),
                                            style: TextStyle(
                                                color: AppColors.colorPrimary,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 12),
                                          ),
                                          const Spacer(),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                right: 10),
                                            child: Text(
                                              announcements[index]
                                                  .author
                                                  .toString(),
                                              style: textstylenotify,
                                            ),
                                          )
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const Divider(
                              thickness: .5,
                              color: Colors.black,
                            )
                          ],
                        ),
                      ),
                    );
                  },
                  itemCount: announcements.length,
                ),
              ),
            ],
          ),
        ),
      );
    }));
  }
}
