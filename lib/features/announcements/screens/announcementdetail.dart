import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/features/announcements/cubit/announcedetail_cubit.dart';
import 'package:legacy/features/announcements/models/announcementmodel.dart';
import 'package:legacy/utils/app_styles.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:loader_overlay/loader_overlay.dart';

import '../../../utils/appcolors.dart';
import '../../../utils/images.dart';
import '../../../utils/progress.dart';
import '../../../utils/timeformat.dart';
import '../models/notificationdetailmodel.dart';

class AnnouncementDetail extends StatefulWidget {
  const AnnouncementDetail({super.key, this.notificationId});
  final notificationId;

  @override
  State<AnnouncementDetail> createState() => _AnnouncementDetailState();
}

class _AnnouncementDetailState extends State<AnnouncementDetail> {
  NotificationDetailModel? announcementsdetail;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context
        .read<AnnouncedetailCubit>()
        .announcementdetaillistLoad(widget.notificationId);
  }

  @override
  Widget build(BuildContext context) {
    return AppLoader(
        child: BlocConsumer<AnnouncedetailCubit, AnnouncedetailState>(
            listener: (context, state) {
      if (state is Loaded) {
        print('55555555${state.announcementsdetails!.data?.author.toString()}');
        setState(() {
          announcementsdetail = state.announcementsdetails;
        });
      }
      // TODO: implement listener
    }, builder: (context, state) {
      (state is Loading)
          ? context.loaderOverlay.show()
          : context.loaderOverlay.hide();
      return Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.colorDarkYellow,
          title: const Text(
            'Announcement',
            style: TextStyle(color: Colors.black,fontSize: 18,fontWeight: FontWeight.bold),
          ),
          centerTitle: true,
          leading: GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: SizedBox(
                  height: 10,
                  child: Image.asset('assets/images/backArrow.png')),
            ),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              gapH12,
              announcementsdetail?.data?.content != null
                  ? Container(
                      width: double.infinity,
                      height: 220,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.black),
                          color: Colors.white),
                      child: Center(
                          child: SizedBox(
                        height: 140,
                        child: announcementsdetail?.data?.content != null
                            ? Image.asset(Images.bg)
                            : Text(''),
                      )),
                    )
                  : Text(''),
              gapH12,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    announcementsdetail?.data?.author.toString() ?? '',
                    style: textstylenotify1,
                  ),
                  announcementsdetail?.data?.content != null
                      ? Text(
                          timeAgo(announcementsdetail?.data?.createdAt ??
                                  DateTime.now()) ??
                              '',
                          style: TextStyle(
                              color: AppColors.colorPrimary,
                              fontSize: 12,
                              fontWeight: FontWeight.bold),
                        )
                      : Text('')
                ],
              ),
              gapH24,
              Text(
                announcementsdetail?.data?.title.toString() ?? '',
                style: textstyle3,
              ),
              gapH16,
              Text(
                announcementsdetail?.data?.content.toString() ?? '',
                style: TextStyle(fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }));
  }
}
