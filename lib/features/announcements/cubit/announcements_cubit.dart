import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../common_widgets/controllers/network_controller.dart';
import '../../../utils/app_pref.dart';
import '../models/announcementmodel.dart';

part 'announcements_state.dart';

class AnnouncementsCubit extends Cubit<AnnouncementsState> {
  AnnouncementsCubit() : super(AnnouncementsInitial());

  Future<void> announcementlistLoad() async {
    emit(Loading());
    String? token = AppPref.getString('token');
    // String? cat_id='626bff2f204c55339bbbb2f5';
    print('qwer$token');

    emit(Loading());
    try {
      NotificationModel body =
          await NetworkController().notificationLoad('$token');

// alertmsg('${AppPref.getString('msg')}');
      print('333333');
      print('000000--------${body.data}');

      emit(Loaded(announcementlistdetails: body));
    } catch (e) {
      emit(Message(e.toString()));
      print('44444441111${e.toString()}');
    }
  }
}
