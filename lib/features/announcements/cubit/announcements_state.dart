part of 'announcements_cubit.dart';

@immutable
abstract class AnnouncementsState {}

class AnnouncementsInitial extends AnnouncementsState {}

class Loading extends AnnouncementsState {
  Loading();
}

class Loaded extends AnnouncementsState {
  final NotificationModel? announcementlistdetails;

  Loaded({this.announcementlistdetails});

  @override
  List<Object> get props => [];
}

class Message extends AnnouncementsState {
  final String message;

  Message(this.message);

  @override
  List<Object> get props => [];
}
