import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../common_widgets/controllers/network_controller.dart';
import '../../../utils/app_pref.dart';
import '../models/notificationdetailmodel.dart';

part 'announcedetail_state.dart';

class AnnouncedetailCubit extends Cubit<AnnouncedetailState> {
  AnnouncedetailCubit() : super(AnnouncedetailInitial());
  Future<void> announcementdetaillistLoad(String notificationId) async {
    emit(Loading());
    String? token = AppPref.getString('token');
    // String? cat_id='626bff2f204c55339bbbb2f5';
    print('qwer$token');

    emit(Loading());
    try {
      await NetworkController()
          .notificationMarkAsRead('$token', notificationId);
      NotificationDetailModel body = await NetworkController()
          .getannouncementdetail('$token', notificationId);

// alertmsg('${AppPref.getString('msg')}');
      print('333333');
      print('000000--------${body.data}');

      emit(Loaded(announcementsdetails: body));
    } catch (e) {
      emit(Message(e.toString()));
      print('444444114${e.toString()}');
    }
  }
}
