part of 'announcedetail_cubit.dart';

@immutable
abstract class AnnouncedetailState {}

class AnnouncedetailInitial extends AnnouncedetailState {}

class Loading extends AnnouncedetailState {
  Loading();
}

class Loaded extends AnnouncedetailState {
  final NotificationDetailModel? announcementsdetails;

  Loaded({this.announcementsdetails});

  @override
  List<Object> get props => [];
}

class Message extends AnnouncedetailState {
  final String message;

  Message(this.message);

  @override
  List<Object> get props => [];
}
