// To parse this JSON data, do
//
//     final notificationDetailModel = notificationDetailModelFromJson(jsonString);

import 'dart:convert';

NotificationDetailModel notificationDetailModelFromJson(String str) =>
    NotificationDetailModel.fromJson(json.decode(str));

String notificationDetailModelToJson(NotificationDetailModel data) =>
    json.encode(data.toJson());

class NotificationDetailModel {
  bool? status;
  String? message;
  Data? data;

  NotificationDetailModel({
    this.status,
    this.message,
    this.data,
  });

  factory NotificationDetailModel.fromJson(Map<String, dynamic> json) =>
      NotificationDetailModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  String? id;
  String? title;
  String? content;
  String? author;
  String? churchId;
  bool? markAsRead;
  String? image;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;

  Data({
    this.id,
    this.title,
    this.content,
    this.author,
    this.churchId,
    this.markAsRead,
    this.image,
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        id: json["_id"],
        title: json["title"],
        content: json["content"],
        author: json["author"],
        churchId: json["churchId"],
        markAsRead: json["markAsRead"],
        image: json["image"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "title": title,
        "content": content,
        "author": author,
        "churchId": churchId,
        "markAsRead": markAsRead,
        "image": image,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };
}
