// To parse this JSON data, do
//
//     final podcastListVideoModel = podcastListVideoModelFromJson(jsonString);

import 'dart:convert';

NotificationModel podcastListVideoModelFromJson(String str) =>
    NotificationModel.fromJson(json.decode(str));

String podcastListVideoModelToJson(NotificationModel data) =>
    json.encode(data.toJson());

class NotificationModel {
  bool? status;
  String? message;
  List<Datum>? data;

  NotificationModel({
    this.status,
    this.message,
    this.data,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      NotificationModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  String? id;
  String? title;
  String? content;
  String? author;
  String? churchId;
  bool? markAsRead;
  String? image;
  DateTime? createdAt;
  DateTime? updatedAt;
  DateTime? date;

  Datum({
    this.id,
    this.title,
    this.content,
    this.author,
    this.churchId,
    this.markAsRead,
    this.image,
    this.createdAt,
    this.updatedAt,
    this.date,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["_id"],
        title: json["title"],
        content: json["content"],
        author: json["author"],
        churchId: json["churchId"],
        markAsRead: json["markAsRead"],
        image: json["image"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "title": title,
        "content": content,
        "author": author,
        "churchId": churchId,
        "markAsRead": markAsRead,
        "image": image,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "date": date?.toIso8601String(),
      };
}
