part of 'podcasts_cubit.dart';

@immutable
abstract class PodcastsState {}

class PodcastsInitial extends PodcastsState {}

class Loading extends PodcastsState {
  Loading();
}

class Loaded extends PodcastsState {
  final PodcastVideomodel? podcastdetails;

  Loaded({this.podcastdetails});

  @override
  List<Object> get props => [];
}

class Message extends PodcastsState {
  final String message;

  Message(this.message);

  @override
  List<Object> get props => [];
}
