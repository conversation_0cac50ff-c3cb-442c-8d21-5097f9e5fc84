import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../common_widgets/controllers/network_controller.dart';
import '../../../utils/app_pref.dart';
import '../models/podcastmodel.dart';

part 'podcasts_state.dart';

class PodcastsCubit extends Cubit<PodcastsState> {
  PodcastsCubit() : super(PodcastsInitial());

  Future<void> podcastvideoLoad() async {
    emit(Loading());
    String? token = AppPref.getString('token');
    print('qwer$token');

    emit(Loading());
    try {
      PodcastVideomodel body =
          await NetworkController().podcastMainAudioLoad('$token');

// alertmsg('${AppPref.getString('msg')}');
      print('333333');

      emit(Loaded(podcastdetails: body));
    } catch (e) {
      emit(Message(e.toString()));
      print('4444444111${e.toString()}');
    }
  }
}
