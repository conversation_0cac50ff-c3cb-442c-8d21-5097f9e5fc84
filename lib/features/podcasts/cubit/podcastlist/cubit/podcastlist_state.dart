part of 'podcastlist_cubit.dart';

@immutable
abstract class PodcastlistState {}

class PodcastlistInitial extends PodcastlistState {}

class Loading extends PodcastlistState {
  Loading();
}

class Loaded extends PodcastlistState {
  final PodcastListModel? podcastlistdetails;

  Loaded({this.podcastlistdetails});

  @override
  List<Object> get props => [];
}

class Message extends PodcastlistState {
  final String message;

  Message(this.message);

  @override
  List<Object> get props => [];
}
