import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../../../common_widgets/controllers/network_controller.dart';
import '../../../../../utils/app_pref.dart';
import '../../../models/podcastlistmodel.dart';

part 'podcastlist_state.dart';

class podcastlistCubit extends Cubit<PodcastlistState> {
  podcastlistCubit() : super(PodcastlistInitial());

  Future<void> podcastlistLoad(String category) async {
    emit(Loading());
    String? token = AppPref.getString('token');
    // String? cat_id='626bff2f204c55339bbbb2f5';
    print('qwer$token');

    emit(Loading());
    try {
      PodcastListModel body =
          await NetworkController().podcastListLoad('$token', category);

// alertmsg('${AppPref.getString('msg')}');
      print('333333');

      emit(Loaded(podcastlistdetails: body));
    } catch (e) {
      emit(Message(e.toString()));
      print('44444441111${e.toString()}');
    }
  }
}
