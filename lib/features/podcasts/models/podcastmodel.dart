// To parse this JSON data, do
//
//     final podcastVideomodel = podcastVideomodelFromJson(jsonString);

import 'dart:convert';

PodcastVideomodel podcastVideomodelFromJson(String str) =>
    PodcastVideomodel.fromJson(json.decode(str));

String podcastVideomodelToJson(PodcastVideomodel data) =>
    json.encode(data.toJson());

class PodcastVideomodel {
  bool? status;
  String? message;
  List<Datum>? data;

  PodcastVideomodel({
    this.status,
    this.message,
    this.data,
  });

  factory PodcastVideomodel.fromJson(Map<String, dynamic> json) =>
      PodcastVideomodel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  String? id;
  String? name;
  String? churchId;
  String? thumbnail;
  String? type;
  DateTime? createdAt;

  Datum({
    this.id,
    this.name,
    this.churchId,
    this.thumbnail,
    this.type,
    this.createdAt,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["_id"],
        name: json["name"],
        churchId: json["churchId"],
        thumbnail: json["thumbnail"],
        type: json["type"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "churchId": churchId,
        "thumbnail": thumbnail,
        "type": type,
        "createdAt": createdAt?.toIso8601String(),
      };
}
