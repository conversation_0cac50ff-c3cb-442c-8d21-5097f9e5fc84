// To parse this JSON data, do
//
//     final podcastListModel = podcastListModelFromJson(jsonString);

import 'dart:convert';

PodcastListModel podcastListModelFromJson(String str) =>
    PodcastListModel.fromJson(json.decode(str));

String podcastListModelToJson(PodcastListModel data) =>
    json.encode(data.toJson());

class PodcastListModel {
  bool? status;
  String? message;
  List<Datum>? data;

  PodcastListModel({
    this.status,
    this.message,
    this.data,
  });

  factory PodcastListModel.fromJson(Map<String, dynamic> json) =>
      PodcastListModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  String? id;
  String? churchId;
  String? title;
  String? description;
  String? author;
  String? duration;
  String? thumbnail;
  String? type;
  String? media;
  String? channelId;
  String? fileName;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;

  Datum({
    this.id,
    this.churchId,
    this.title,
    this.description,
    this.author,
    this.duration,
    this.thumbnail,
    this.type,
    this.media,
    this.channelId,
    this.fileName,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["_id"],
        churchId: json["churchId"],
        title: json["title"],
        description: json["description"],
        author: json["author"],
        duration: json["duration"],
        thumbnail: json["thumbnail"],
        type: json["type"],
        media: json["media"],
        channelId: json["channelId"],
        fileName: json["fileName"],
        status: json["status"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "churchId": churchId,
        "title": title,
        "description": description,
        "author": author,
        "duration": duration,
        "thumbnail": thumbnail,
        "type": type,
        "media": media,
        "channelId": channelId,
        "fileName": fileName,
        "status": status,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };
}
