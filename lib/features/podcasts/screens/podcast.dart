import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/common_widgets/sidedrawer/side_drawer.dart';
import 'package:legacy/features/podcasts/cubit/podcasts_cubit.dart';
import 'package:legacy/features/podcasts/models/podcastmodel.dart';
import 'package:legacy/features/podcasts/screens/podcastlistscreen.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import '../../../main.dart';
import '../../../utils/app_styles.dart';
import '../../../utils/images.dart';
import '../../../utils/progress.dart';

class Podcasts extends StatefulWidget {
  const Podcasts({super.key});

  @override
  State<Podcasts> createState() => _PodcastsState();
}

class _PodcastsState extends State<Podcasts> {
  List<Datum> podcastaudios = [];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<PodcastsCubit>().podcastvideoLoad();
  }

  final GlobalKey<ScaffoldState> _key = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return AppLoader(
        child: BlocConsumer<PodcastsCubit, PodcastsState>(
            listener: (context, state) {
      if (state is Loaded) {
        print('55555555${state.podcastdetails!.data}');
        setState(() {
          podcastaudios = state.podcastdetails?.data as List<Datum>;
        });
      }
      // TODO: implement listener
    }, builder: (context, state) {
      (state is Loading)
          ? context.loaderOverlay.show()
          : context.loaderOverlay.hide();
      return Scaffold(
        drawerEnableOpenDragGesture: false,
        key: _key,
        appBar: AppBar(
          systemOverlayStyle:
              const SystemUiOverlayStyle(statusBarColor: Colors.black),
          leading: IconButton(
            icon: Image.asset(
              Images.menuBarIcon,
              scale: 2.5,
            ),
            onPressed: () {
              pushNewScreen(
                context,
                screen: DrawerWidget(),
                withNavBar: false, // OPTIONAL VALUE. True by default.
                pageTransitionAnimation: PageTransitionAnimation.slideRight,
              );
            },
          ),
          centerTitle: true,
          toolbarHeight: 80,
          title: SizedBox(
              height: 70,
              child: Image.asset(
                'assets/images/legacyleadersupdatelogo.png',
              )),
          backgroundColor: Colors.black,
        ),
        body: SizedBox(
          height: MediaQuery.of(context).size.height,
          child: Column(
            children: [
              Container(
                  height: 60,
                  color: const Color.fromARGB(255, 0, 0, 0),
                  width: double.infinity,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 15, vertical: 15),
                            child: SizedBox(
                                height: 22,
                                child: Image.asset(Images.podcastIcon2)),
                          ),
                          Text('PODCASTS', style: textstyleheading),
                        ],
                      ),
                    ],
                  )),
              // Expanded(
              //   child: GridView.builder(
              //     itemCount: podcastNames.length,
              //     gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              //         crossAxisCount: 2),
              //     itemBuilder: (BuildContext context, int index) {
              //       return Padding(
              //         padding: const EdgeInsets.all(8.0),
              //         child: Container(
              //           decoration: BoxDecoration(
              //               borderRadius: BorderRadius.circular(10),
              //               image: DecorationImage(
              //                   image: AssetImage(channelImage[0]),
              //                   fit: BoxFit.cover)),
              //           child: Padding(
              //             padding: const EdgeInsets.all(15.0),
              //             child: Column(
              //               mainAxisAlignment: MainAxisAlignment.end,
              //               crossAxisAlignment: CrossAxisAlignment.start,
              //               children: [
              //                 SizedBox(
              //                   height: 30,
              //                   width: 30,
              //                   child: Image.asset(Images.channelIcon),
              //                 ),
              //                 Padding(
              //                   padding: const EdgeInsets.only(bottom: 20),
              //                   child: Text(
              //                     podcastNames[index],
              //                     style: const TextStyle(
              //                         fontSize: 18,
              //                         fontWeight: FontWeight.bold,
              //                         color: Colors.white),
              //                   ),
              //                 )
              //               ],
              //             ),
              //           ),
              //         ),
              //       );
              //     },
              //   ),
              // )
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(5),
                  child: GridView.builder(
                    itemCount: podcastaudios.length,
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                            childAspectRatio: 3.11 / 2.0,
                            mainAxisSpacing: 5,
                            crossAxisSpacing: 5,
                            crossAxisCount: 2),
                    itemBuilder: (BuildContext context, int index) {
                      // AppPref.setString('podcastcategory$index',podcastaudios[index].id.toString() );

                      return GestureDetector(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) => Podcastlisting(
                                    name: podcastaudios[index].name.toString(),
                                    category:
                                        podcastaudios[index].id.toString(),
                                  )));
                        },
                        child: Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              image: DecorationImage(
                                  colorFilter: ColorFilter.mode(
                                      Colors.black.withOpacity(0.3),
                                      BlendMode.darken),
                                  image: CachedNetworkImageProvider(
                                      podcastaudios[index]
                                          .thumbnail
                                          .toString()),
                                  fit: BoxFit.cover)),
                          child: Padding(
                            padding: const EdgeInsets.all(15.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: podcastaudios[index].thumbnail != null
                                      ? Image.asset(Images.podcastIcon)
                                      : Image.network(''),
                                ),
                                Padding(
                                    padding: const EdgeInsets.only(bottom: 4),
                                    child:
                                        podcastaudios[index].thumbnail != null
                                            ? Text(
                                                podcastaudios[index]
                                                    .name
                                                    .toString()
                                                    .toUpperCase(),
                                                style: textstyle1)
                                            : Text(''))
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              )
            ],
          ),
        ),
      );
    }));
  }
}
