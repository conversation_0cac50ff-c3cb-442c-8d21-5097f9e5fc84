import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/common_widgets/musicplayer/music.dart';
import 'package:legacy/features/podcasts/models/podcastlistmodel.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import '../../../utils/app_pref.dart';
import '../../../utils/app_styles.dart';
import '../../../utils/appcolors.dart';
import '../../../utils/gaps.dart';
import '../../../utils/images.dart';
import '../../../utils/progress.dart';
import '../cubit/podcastlist/cubit/podcastlist_cubit.dart';

class Podcastlisting extends StatefulWidget {
  const Podcastlisting({super.key, this.name, this.category});

  final name;
  final category;

  @override
  State<Podcastlisting> createState() => _PodcastlistingState();
}

class _PodcastlistingState extends State<Podcastlisting> {
  List<Datum> podcastListFull = [];
  List<Datum> podcastListFiltered = [];

  var controller = TextEditingController();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<podcastlistCubit>().podcastlistLoad(widget.category);
  }

  void resetSearch() {
    setState(() {
      podcastListFiltered.clear();
      podcastListFiltered = podcastListFull;
    });
  }

  // List<Datum> podcastlistvideos = [];

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            body: AppLoader(
      child: BlocConsumer<podcastlistCubit, PodcastlistState>(
          listener: (context, state) {
        if (state is Loaded) {
          print('55555555${state.podcastlistdetails!.data}');
          // setState(() {
          //   podcastlistvideos = state.podcastlistdetails?.data as List<Datum>;
          // });
          setState(() {
            podcastListFull = state.podcastlistdetails?.data as List<Datum>;
            podcastListFiltered = podcastListFull;
          });
        }
        // TODO: implement listener
      }, builder: (context, state) {
        (state is Loading)
            ? context.loaderOverlay.show()
            : context.loaderOverlay.hide();
        return Column(
          children: [
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                  // color: Colors.red,
                  image: DecorationImage(
                      colorFilter: ColorFilter.mode(
                          Colors.black.withOpacity(0.5), BlendMode.darken),
                      image: const AssetImage('assets/images/tile_top_bg.png'),
                      fit: BoxFit.cover)),
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: SizedBox(
                                height: 20,
                                child:
                                    Image.asset('assets/images/backArrow.png')),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 20),
                          child: Text(widget.name, style: textstyleheading),
                        )
                      ],
                    ),
                    gapH16,
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.black.withOpacity(.3),
                            borderRadius: BorderRadius.circular(8)),
                        height: 45,
                        child: Center(
                          child: TextFormField(
                            style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold),
                            // validator: validateEmail,
                            // controller: emailcontroller,
                            controller: controller,

                            onChanged: (value) {
                              print(
                                  '--------------------------on changed------------------------------');
                              List<Datum> tempList = List.empty(growable: true);
                              setState(() {
                                if (controller.value.text.isEmpty) {
                                  resetSearch();
                                }
                                for (var i in podcastListFull) {
                                  if (i.title!
                                      .toLowerCase()
                                      .contains(controller.value.text)) {
                                    tempList.add(i);
                                  }
                                }
                                // channelListFiltered.clear();
                                podcastListFiltered = tempList;
                              });
                            },
                            decoration: InputDecoration(
                                hintStyle: TextStyle(
                                    color: Colors.white.withOpacity(.5),
                                    fontSize: 15),
                                prefixIcon: Padding(
                                  padding: const EdgeInsets.all(15.0),
                                  child: Image.asset(
                                    'assets/images/SearchIcon.png',
                                    height: 20,
                                    width: 10,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: AppColors.colorPrimary),
                                    borderRadius: BorderRadius.circular(8)),
                                border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8)),
                                // labelText: "Search Audios",
                                // labelStyle: TextStyle(color: AppColors.colorPrimary),
                                hintText: "Search Audios"),
                          ),
                        ),
                      ),
                    ),
                  ]),
            ),
            Expanded(
              child: SizedBox(
                  child: (podcastListFiltered.isNotEmpty)
                      ? ListView.builder(
                          // separatorBuilder: (BuildContext context, index) {
                          //   return const Divider(
                          //     thickness: 0,
                          //     height: 1,
                          //     color: Colors.black,
                          //   );
                          // },
                          itemBuilder: (BuildContext context, index) {
                            return GestureDetector(
                                onTap: () {
                                  // AppPref.setString('vimeoId',podcastlistvideos[index].media?? 'j');

                                  // String? token1 = AppPref.getString('token');
                                  pushNewScreen(
                                    context,
                                    screen: Music(
                                      music: podcastListFiltered[index].media,
                                      thumbnail:
                                          podcastListFiltered[index].thumbnail,
                                      title: podcastListFiltered[index].title,
                                      subtitle: podcastListFiltered[index]
                                          .description,
                                    ),
                                    withNavBar:
                                        false, // OPTIONAL VALUE. True by default.
                                    pageTransitionAnimation:
                                        PageTransitionAnimation.cupertino,
                                  );
                                },
                                child: Card(
                                  child: Container(
                                      margin: const EdgeInsets.all(5),
                                      height: 95,
                                      // color: Colors.white,
                                      child: Row(
                                        children: [
                                          gapW8,
                                          Container(
                                            height: 90,
                                            width: 95,
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                // border: Border.all(),
                                                // color: Colors.red,
                                                image: DecorationImage(
                                                  image:
                                                      CachedNetworkImageProvider(
                                                          podcastListFiltered[
                                                                      index]
                                                                  .thumbnail ??
                                                              ''),
                                                  fit: BoxFit.cover,
                                                )),
                                            child: Center(
                                              child: SizedBox(
                                                height: 40,
                                                child: Image.asset(
                                                  'assets/images/roundaudio.png',
                                                  // color: Colors.white,
                                                ),
                                              ),
                                            ),
                                          ),
                                          gapW8,
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                gapH12,
                                                Text(
                                                  podcastListFiltered[index]
                                                      .title
                                                      .toString()
                                                      .toUpperCase(),
                                                  style: const TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 16),
                                                ),
                                                gapH16,
                                                Text(
                                                  podcastListFiltered[index]
                                                      .description
                                                      .toString(),
                                                  style: const TextStyle(
                                                      // color: AppColors.colorPrimary,
                                                      // fontWeight: FontWeight.bold,
                                                      fontSize: 15),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      )),
                                ));
                          },
                          itemCount: podcastListFiltered.length,
                        )
                      : Center(
                          child: Text(
                          'No Audios Available',
                          style: textstyleheading1,
                        ))),
            )
          ],
        );
      }),
    )));
  }
}
