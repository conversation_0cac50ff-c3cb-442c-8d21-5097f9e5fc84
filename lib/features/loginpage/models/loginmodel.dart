// To parse this JSON data, do
//
//     final loginCred = loginCredFromJson(jsonString);

import 'dart:convert';

LoginCred loginCredFromJson(String str) => LoginCred.fromJson(json.decode(str));

String loginCredToJson(LoginCred data) => json.encode(data.toJson());

class LoginCred {
  LoginCred({
    this.message,
    this.status,
    this.data,
  });

  String? message;
  bool? status;
  Data? data;

  factory LoginCred.fromJson(Map<String, dynamic> json) => LoginCred(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  Data({
    this.token,
    this.refreshToken,
    this.userId,
    this.name,
    this.role,
    this.expiresIn,
    this.vimeoId,
  });

  String? token;
  String? refreshToken;
  String? userId;
  String? name;
  String? role;
  String? expiresIn;
  String? vimeoId;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        token: json["token"],
        refreshToken: json["refreshToken"],
        userId: json["userId"],
        name: json["name"],
        role: json["role"],
        expiresIn: json["expiresIn"],
        vimeoId: json["vimeoId"],
      );

  Map<String, dynamic> toJson() => {
        "token": token,
        "refreshToken": refreshToken,
        "userId": userId,
        "name": name,
        "role": role,
        "expiresIn": expiresIn,
        "vimeoId": vimeoId,
      };
}
