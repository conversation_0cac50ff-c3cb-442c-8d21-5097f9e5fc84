import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/main.dart';
import 'package:legacy/utils/app_pref.dart';
import 'package:legacy/utils/app_styles.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:legacy/utils/images.dart';
import 'package:legacy/utils/progress.dart';
import 'package:loader_overlay/loader_overlay.dart';

import '../../common_widgets/formfield.dart';
import '../../common_widgets/tools.dart';
import '../../utils/appcolors.dart';
import '../forgetpassword/forget_password_page.dart';
import '../signin/signin.dart';
import 'loginbloc/cubit/login_cubit.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  TextEditingController emailcontroller = TextEditingController();
  TextEditingController pwdcontroller = TextEditingController();

  bool isPasswordHidden = true;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: (context) => LoginCubit(),
        child: AppLoader(
          child: Scaffold(
              // resizeToAvoidBottomInset: false,
              body: BlocConsumer<LoginCubit, LoginState>(
                  listener: (context, state) {
            if (state is MessageLogin) {
              alertmsg(state.message, context);
            }
            if (state is Loaded) {
              alertmsg(state.loginDetails?.message, context);
              if (state.loginDetails?.status ?? false) {
                var details = state.loginDetails?.data;
                AppPref.setString('usr', details?.name?.toString() ?? 'j');
                AppPref.setString('LOGIN_DETAILS', details.toString());
                AppPref.setString('token', details?.token?.toString() ?? 'k');

                AppPref.setString(
                    'ref_token', details?.refreshToken?.toString() ?? 'k');
                AppPref.setString('usr_id', details?.userId?.toString() ?? 'k');
                AppPref.setString(
                    'vimeo_id', details?.vimeoId?.toString() ?? 'k');
                AppPref.setBool('loggedIn', true);
                print("-------22222-----------${AppPref.getString('token')}");
                AppPref.setString('email_id', emailcontroller.text);
                AppPref.setString('withoutlog', '0');

                Navigator.of(context).pushNamedAndRemoveUntil(
                    '/home', (Route<dynamic> route) => false);
              }
            }
            (state is Loading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
          }, builder: (context, state) {
            return SingleChildScrollView(
              child: Container(
                height: MediaQuery.of(context).size.height,
                width: double.infinity,
                decoration: BoxDecoration(
                    image: DecorationImage(
                        image: AssetImage(
                          Images.loginIcon,
                        ),
                        colorFilter: ColorFilter.mode(
                            Colors.black.withOpacity(0.5), BlendMode.darken),
                        fit: BoxFit.fill)),
                child: Stack(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                          top: MediaQuery.of(context).size.height / 4),
                      child: Container(
                        padding: const EdgeInsets.all(24),
                        height: MediaQuery.of(context).size.height,
                        width: double.infinity,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(150),
                          ),
                        ),
                        child: Column(children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 20.0),
                            child: SizedBox(
                              height: 150,
                              width: 150,
                              child: Image.asset(
                                Images.bg,
                              ),
                            ),
                          ),
                          Form(
                            autovalidateMode: AutovalidateMode.always,
                            child: TextFormField(
                              autofocus: false,
                              controller: emailcontroller,
                              validator: validateEmail,
                              decoration: InputDecoration(
                                  focusedBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                          color: AppColors.colorPrimary),
                                      borderRadius: BorderRadius.circular(16)),
                                  prefixIcon: Padding(
                                    padding: const EdgeInsets.all(15.0),
                                    child: Image.asset(
                                      Images.userIcon,
                                      height: 20,
                                      width: 10,
                                    ),
                                  ),
                                  border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(16)),
                                  labelText: "Email",
                                  labelStyle: TextStyle(
                                      color: AppColors.colorPrimary,
                                      fontFamily: ''),
                                  hintText: "Enter your email here"),
                            ),
                          ),
                          gapH16,
                          AppFormField(
                            icon_suffix: IconButton(
                              icon: Container(height: 18,width:24,decoration: BoxDecoration(image:DecorationImage(image: AssetImage(!isPasswordHidden
                                  ?Images.visibilityOff: Images.visibility
                                   ),fit: BoxFit.cover,))),
                              onPressed: () => setState(() {
                                isPasswordHidden = !isPasswordHidden;
                              }),
                            ),
                            obs: isPasswordHidden,
                            label: 'Password',
                            controller: pwdcontroller,
                            hint: 'enter password here',
                            icon: Images.pwdIcon,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              TextButton(
                                child: Text(
                                  "Forgot Password?",
                                  style: textstyletextfield,
                                ),
                                onPressed: () => Navigator.of(context).push(
                                    MaterialPageRoute(
                                        builder: (BuildContext context) =>
                                            const ForgetPasswordPage())),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 55,
                            child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xffE0BE6B),
                                  foregroundColor: Colors.black,
                                  minimumSize: const Size(double.infinity, 40),
                                  shape: RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadius.circular(32.0)),
                                ),
                                onPressed: () {
                                  if ((pwdcontroller.text.isEmpty) ||
                                      (emailcontroller.text.isEmpty)) {
                                    alertmsg('Fill required fields', context);
                                  } else {
                                    // NetworkController().LoginAuth(emailcontroller.text, pwdcontroller.text);
                                    context.read<LoginCubit>().loggingIn(
                                        emailcontroller.text,
                                        pwdcontroller.text);

                                    print(AppPref.getString('msg'));
                                    // print( AppPref.getString('LOGIN_DETAILS'));
                                    // print( AppPref.getString('token'));
                                    AppPref.getString('status');
                                    AppPref.getString('status');
                                    AppPref.getString('status');
                                    print(
                                        '12121212${AppPref.getString('status')}');
                                  }
                                },
                                child: const Text('SIGN IN',
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 15))),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "Don't have an account?",
                                style: textstyletextfield,
                              ),
                              TextButton(
                                  child: const Text(
                                    'Sign Up',
                                    style: TextStyle(
                                        decoration: TextDecoration.underline,
                                        color: Colors.black,
                                        fontWeight: FontWeight.normal),
                                  ),
                                  onPressed: () {
                                    Navigator.of(context).pushReplacement(
                                        MaterialPageRoute(
                                            builder: (BuildContext co) =>
                                                const SignIn()));
                                  }),
                            ],
                          ),
                        ]),
                      ),
                    ),
                  ],
                ),
              ),
            );
          })),
        ));
  }

  String? validateEmail(String? value) {
    const pattern = r"(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'"
        r'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-'
        r'\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*'
        r'[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4]'
        r'[0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9]'
        r'[0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\'
        r'x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])';
    final regex = RegExp(pattern);

    return value!.isNotEmpty && !regex.hasMatch(value)
        ? 'Enter a valid email address'
        : null;
  }
}
