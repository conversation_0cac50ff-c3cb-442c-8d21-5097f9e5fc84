part of 'login_cubit.dart';

abstract class LoginState {}

class Loading extends LoginState {
  @override
  List<Object> get props => [];
}

class Loaded extends LoginState {
  final LoginCred? loginDetails;

  Loaded({this.loginDetails});

  @override
  List<Object> get props => [];
}

class MessageLogin extends LoginState {
  final String message;

  MessageLogin(this.message);

  @override
  List<Object> get props => [];
}
