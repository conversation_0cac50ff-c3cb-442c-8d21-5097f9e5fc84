import 'package:bloc/bloc.dart';

import '../../../../common_widgets/controllers/network_controller.dart';
import '../../models/loginmodel.dart';

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit() : super(Loading());

  Future<void> loggingIn(String username, String password) async {
    emit(Loading());
    try {
      LoginCred body = await NetworkController().loginAuth(username, password);
      print(body);
      emit(Loaded(loginDetails: body));
    } catch (e) {
      emit(MessageLogin(e.toString()));
      print(e);
    }
  }
}
