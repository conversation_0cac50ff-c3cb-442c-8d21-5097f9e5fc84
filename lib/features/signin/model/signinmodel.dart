// To parse this JSON data, do
//
//     final signInModel = signInModelFromJson(jsonString);

import 'dart:convert';

SignInModel signInModelFromJson(String str) =>
    SignInModel.fromJson(json.decode(str));

String signInModelToJson(SignInModel data) => json.encode(data.toJson());

class SignInModel {
  SignInModel({
    this.status,
    this.message,
    this.validation,
  });

  bool? status;
  String? message;
  List<Validation>? validation;

  factory SignInModel.fromJson(Map<String, dynamic> json) => SignInModel(
        status: json["status"],
        message: json["message"],
        validation: json["validation"] == null
            ? []
            : List<Validation>.from(
                json["validation"]!.map((x) => Validation.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "validation": validation == null
            ? []
            : List<dynamic>.from(validation!.map((x) => x.toJson())),
      };
}

class Validation {
  Validation({
    this.field,
    this.message,
  });

  String? field;
  String? message;

  factory Validation.fromJson(Map<String, dynamic> json) => Validation(
        field: json["field"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "field": field,
        "message": message,
      };
}
