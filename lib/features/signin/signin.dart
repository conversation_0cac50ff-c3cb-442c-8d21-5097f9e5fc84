import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/features/loginpage/loginpage.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:legacy/utils/images.dart';

import '../../common_widgets/formfield.dart';
import '../../common_widgets/tools.dart';
import '../../utils/app_styles.dart';
import '../../utils/appcolors.dart';
import '../forgetpassword/forget_password_page.dart';
import 'cubit/sign_in_app_cubit.dart';

class SignIn extends StatefulWidget {
  const SignIn({super.key});

  @override
  State<SignIn> createState() => _SignInState();
}

class _SignInState extends State<SignIn> {
  TextEditingController emailcontroller = TextEditingController();
  TextEditingController pwdcontroller = TextEditingController();
  TextEditingController namecontroller = TextEditingController();
  TextEditingController confirmpwdcontroller = TextEditingController();

  bool isPasswordHidden = true;
  bool isPasswordHidden1 = true;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: (context) => SignInAppCubit(),
        child: Scaffold(
            backgroundColor: Colors.white,
            body: BlocConsumer<SignInAppCubit, SignInAppState>(
                listener: (context, state) {
              if (state is Loaded) {
                if (state.signDetails?.status ?? false) {
                  print('1212121212121aaaaaaa');

                  alertmsg(state.signDetails?.message, context);
                  Navigator.of(context).pushReplacement(MaterialPageRoute(
                      builder: (BuildContext) => const LoginPage()));
                } else {
                  print('1212121212121');
                  if (state.signDetails?.validation != null) {
                    alertmsg(state.signDetails?.message, context);
                  }
                  state.signDetails?.validation?.forEach((element) {
                    alertmsg(element.message, context);
                  });
                }
                // TODO: implement listener
              }
            }, builder: (context, state) {
              return SingleChildScrollView(
                  child: Padding(
                padding: const EdgeInsets.all(18.0),
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      gapH32,
                      Padding(
                        padding: const EdgeInsets.only(top: 50.0, bottom: 50),
                        child: SizedBox(
                          height: 120,
                          width: 120,
                          child: Image.asset(
                            Images.bg,
                          ),
                        ),
                      ),
                      TextFormField(
                        controller: namecontroller,
                        // validator: validateEmail,
                        decoration: InputDecoration(
                            focusedBorder: OutlineInputBorder(
                                borderSide:
                                    BorderSide(color: AppColors.colorPrimary),
                                borderRadius: BorderRadius.circular(16)),
                            prefixIcon: Padding(
                              padding: const EdgeInsets.all(15.0),
                              child: Image.asset(
                                Images.userIcon,
                                height: 20,
                                width: 10,
                              ),
                            ),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(16)),
                            labelText: "Name",
                            labelStyle:
                                TextStyle(color: AppColors.colorPrimary),
                            hintText: "Enter your name"),
                      ),
                      // AppFormField(
                      //     label: 'Name',
                      //     controller: namecontroller,
                      //     hint: 'Enter your name',
                      //     icon: Images.userIcon,
                      //     icon_suffix: IconButton(
                      //       icon: const Icon(
                      //         Icons.abc,
                      //         color: Colors.white,
                      //       ),
                      //       onPressed: () {},
                      //     ),
                      //     obs: false),
                      gapH12,
                      Form(
                        autovalidateMode: AutovalidateMode.always,
                        child: TextFormField(
                          controller: emailcontroller,
                          validator: validateEmail,
                          decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                  borderSide:
                                      BorderSide(color: AppColors.colorPrimary),
                                  borderRadius: BorderRadius.circular(16)),
                              prefixIcon: Padding(
                                padding: const EdgeInsets.all(15.0),
                                child: Image.asset(
                                  Images.emailIcon,
                                  height: 20,
                                  width: 10,
                                ),
                              ),
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16)),
                              labelText: "Email",
                              labelStyle:
                                  TextStyle(color: AppColors.colorPrimary),
                              hintText: "Enter your email here"),
                        ),
                      ),
                      gapH12,
                      AppFormField(
                        icon_suffix: IconButton(
                          icon:Container(height: 18,width:24,decoration: BoxDecoration(image:DecorationImage(image: AssetImage(!isPasswordHidden
                                  ?Images.visibilityOff: Images.visibility
                                   ),fit: BoxFit.cover,))),
                              onPressed: () => setState(() {
                                isPasswordHidden = !isPasswordHidden;
                              }),
                         
                        ),
                        obs: isPasswordHidden,
                        label: 'Password',
                        controller: pwdcontroller,
                        hint: 'enter password here',
                        icon: Images.pwdIcon,
                      ),
                      gapH12,
                      AppFormField(
                        icon_suffix: IconButton(
                          icon: Container(height: 18,width:24,decoration: BoxDecoration(image:DecorationImage(image: AssetImage(!isPasswordHidden1
                                  ?Images.visibilityOff: Images.visibility
                                   ),fit: BoxFit.cover,))),
                              onPressed: () => setState(() {
                                isPasswordHidden1 = !isPasswordHidden1;
                              }),
                        ),
                        obs: isPasswordHidden1,
                        label: 'Confirm Password',
                        controller: confirmpwdcontroller,
                        hint: 'retype password here',
                        icon: Images.pwdIcon,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            child: Text(
                              "Forgot Password?",
                              style: textstyletextfield,
                            ),
                            onPressed: () => Navigator.of(context).push(
                                MaterialPageRoute(
                                    builder: (BuildContext context) =>
                                        const ForgetPasswordPage())),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 55,
                        child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xffE0BE6B),
                              foregroundColor: Colors.black,
                              minimumSize: const Size(double.infinity, 40),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(32.0)),
                            ),
                            onPressed: () {
                              if ((namecontroller.text.isEmpty) ||
                                  (pwdcontroller.text.isEmpty) ||
                                  (emailcontroller.text.isEmpty)) {
                                alertmsg('Fill required fields', context);
                              } else if (pwdcontroller.text !=
                                  confirmpwdcontroller.text) {
                                alertmsg(
                                    '''passwords doesn't match''', context);
                              } else {
                                context.read<SignInAppCubit>().signingIn(
                                    emailcontroller.text,
                                    pwdcontroller.text,
                                    namecontroller.text);
                              }

                              // print( AppPref.getString('LOGIN_DETAILS'));
                              // print( AppPref.getString('token'));
                            },
                            child: const Text(
                              'SIGN UP',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 15),
                            )),
                      ),
                      Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Already a member?",
                              style: textstyletextfield,
                            ),
                            TextButton(
                                child: const Text(
                                  'Sign In',
                                  style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      color: Colors.black,
                                      fontWeight: FontWeight.normal),
                                ),
                                onPressed: () {
                                  Navigator.of(context).pushReplacement(
                                      MaterialPageRoute(
                                          builder: (BuildContext context) =>
                                              const LoginPage()));
                                }),
                          ]),
                    ]),
              ));
            })));
  }

  String? validateEmail(String? value) {
    const pattern = r"(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'"
        r'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-'
        r'\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*'
        r'[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4]'
        r'[0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9]'
        r'[0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\'
        r'x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])';
    final regex = RegExp(pattern);

    return value!.isNotEmpty && !regex.hasMatch(value)
        ? 'Enter a valid email address'
        : null;
  }
}
