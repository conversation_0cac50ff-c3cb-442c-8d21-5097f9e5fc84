part of 'sign_in_app_cubit.dart';

abstract class SignInAppState {}

class SignInAppInitial extends SignInAppState {}

class Loading extends SignInAppState {
  @override
  List<Object> get props => [];
}

class Loaded extends SignInAppState {
  final SignInModel? signDetails;

  Loaded({this.signDetails});

  @override
  List<Object> get props => [];
}

class Message extends SignInAppState {
  final String msg;

  Message(this.msg);

  @override
  List<Object> get props => [];
}
