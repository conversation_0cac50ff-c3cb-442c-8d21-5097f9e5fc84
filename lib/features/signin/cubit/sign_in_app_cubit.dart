import 'package:bloc/bloc.dart';

import '../../../common_widgets/controllers/network_controller.dart';
import '../../../common_widgets/tools.dart';
import '../../../utils/app_pref.dart';
import '../model/signinmodel.dart';

part 'sign_in_app_state.dart';

class SignInAppCubit extends Cubit<SignInAppState> {
  SignInAppCubit() : super(SignInAppInitial());

  Future<void> signingIn(String username, String password, String name) async {
    emit(Loading());
    try {
      SignInModel body =
          await NetworkController().signingIn(username, password, name);

      emit(Loaded(signDetails: body));
    } catch (e) {
      emit(Message(e.toString()));
    }
  }
}
