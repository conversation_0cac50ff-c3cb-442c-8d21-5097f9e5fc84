import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:legacy/utils/fonts.dart';

var textstyle1 = GoogleFonts.poppins()
    .copyWith(fontSize: 15, fontWeight: FontWeight.bold, color: Colors.white);
var textstyle2 = GoogleFonts.poppins().copyWith(
    fontSize: 24,
    fontWeight: FontWeight.w900,
    color: Colors.white,
    fontFamily: Fonts.font3);
var textstyle3 = GoogleFonts.poppins().copyWith(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: Colors.black,
    fontFamily: Fonts.font10);
var textstyleheading = GoogleFonts.poppins()
    .copyWith(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white);
var textstyle4 =
    GoogleFonts.poppins().copyWith(fontSize: 16, color: Colors.white);
var textstylenotification = GoogleFonts.poppins()
    .copyWith(fontSize: 12, color: Colors.white, fontWeight: FontWeight.bold);
var textstylenotify = GoogleFonts.poppins()
    .copyWith(fontSize: 10, color: Colors.black, fontStyle: FontStyle.italic);
var textstylenotify1 = GoogleFonts.poppins()
    .copyWith(fontSize: 12, color: Colors.black, fontStyle: FontStyle.italic);
var textstylesplash = GoogleFonts.poppins()
    .copyWith(fontSize: 30, fontWeight: FontWeight.w700, color: Colors.white);
var textstylesplash2 = GoogleFonts.poppins()
    .copyWith(fontSize: 10, fontWeight: FontWeight.w900, color: Colors.white);
var textstyletextfield = GoogleFonts.poppins()
    .copyWith(fontWeight: FontWeight.normal, color: Colors.black, fontSize: 14);
var textstylethumbnail = GoogleFonts.poppins().copyWith(
    color: Colors.white,
    fontSize: 20,
    fontFamily: Fonts.font10,
    fontWeight: FontWeight.bold);
var textstylesubthumbnail = GoogleFonts.poppins().copyWith(
    color: Colors.white,
    fontSize: 12,
    fontFamily: Fonts.font3,
    fontWeight: FontWeight.bold);
var textstyleheading1 = GoogleFonts.poppins()
    .copyWith(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.black);
