import 'dart:convert';

List<String> getVideoTitles(String response) {
  List<String> titles = [];
  Map<String, dynamic> jsonResponse = json.decode(response);
  List<dynamic> data = jsonResponse['data'];
  for (var video in data) {
    titles.add(video['title']);
  }
  return titles;
}

class Video {
  String id;
  String churchId;
  String title;
  String description;
  String author;
  int duration;
  String thumbnail;
  String type;
  int media;
  String channelId;
  String vimeoId;
  String status;
  String createdAt;
  String updatedAt;
  int v;

  Video.fromJson(Map<String, dynamic> json)
      : id = json['_id'],
        churchId = json['churchId'],
        title = json['title'],
        description = json['description'],
        author = json['author'],
        duration = json['duration'],
        thumbnail = json['thumbnail'],
        type = json['type'],
        media = json['media'],
        channelId = json['channelId'],
        vimeoId = json['vimeoId'],
        status = json['status'],
        createdAt = json['createdAt'],
        updatedAt = json['updatedAt'],
        v = json['__v'];

  Map<String, dynamic> toJson() => {
        '_id': id,
        'churchId': churchId,
        'title': title,
        'description': description,
        'author': author,
        'duration': duration,
        'thumbnail': thumbnail,
        'type': type,
        'media': media,
        'channelId': channelId,
        'vimeoId': vimeoId,
        'status': status,
        'createdAt': createdAt,
        'updatedAt': updatedAt,
        '__v': v,
      };
}

List<Video> responseToList(String response) {
  final parsed = jsonDecode(response);
  final data = parsed['data'] as List<dynamic>;
  return data.map((json) => Video.fromJson(json)).toList();
}
