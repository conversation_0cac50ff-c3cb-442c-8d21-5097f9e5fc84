import 'package:flutter/material.dart';

import 'images.dart';

const baseAppUrl = "https://backend.custom-app.com/api";
// const baseAppUrl = "https://backend.stage.custom-app.com/api/";

List scrollImage = [
  'assets/images/include_ic2.png',
  'assets/images/include_ic1.png',
  'assets/images/announment2.png',
  'assets/images/article2.png',
  'assets/images/chatimage1.png',
  'assets/images/eventsnew.png'
];
List scrollNames = [
  'CHANNELS',
  'PODCASTS',
  'ANNOUNCEMENTS',
  'ARTICLES',
  'MESSAGE BOARD',
  'EVENTS'
];
List channelImage = ['assets/images/authloadimg.png'];
List channelNames = [
  'COMMUNITY YARD SALE',
  'MOVIE NIGHT',
  'WORSHIP VIDEO',
];
List podcastNames = [
  'VACATION BIBLE SCHOOL',
  'CHURCH PICNIC',
  'WORSHIP SONG',
  'YOUTH MEETING',
  'TESTING',
];
List sideDrawerNames = [
  'Home',
  'Article',
  'Message Board',
  'Events',
  'Channels',
  'Podcasts',
  'Announcements'
];
List sideDrawerIcons = [
  Images.drawerhome,
  Images.drawerarticle,
  Images.drawermessage,
  Images.drawerevent,
  Images.drawerchannel,
  Images.drawerpodcast,
  Images.drawerannouncement,
];
List<Color> sideDrawerColor = [
  Colors.black.withOpacity(0.2),
  Colors.black.withOpacity(0.4),
  Colors.black.withOpacity(0.2),
  Colors.black.withOpacity(0.4),
  Colors.black.withOpacity(0.2),
  Colors.black.withOpacity(0.4),
  Colors.black.withOpacity(0.2),
];
