import 'dart:convert';

LogoutAppModel logoutAppModelFromJson(String str) =>
    LogoutAppModel.fromJson(json.decode(str));

String logoutAppModelToJson(LogoutAppModel data) => json.encode(data.toJson());

class LogoutAppModel {
  bool? status;
  String? message;

  LogoutAppModel({
    this.status,
    this.message,
  });

  factory LogoutAppModel.fromJson(Map<String, dynamic> json) => LogoutAppModel(
        status: json["status"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
      };
}
