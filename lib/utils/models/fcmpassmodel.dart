import 'dart:convert';

FcmPass fcmPassFromJson(String str) => FcmPass.fromJson(json.decode(str));

String fcmPassToJson(FcmPass data) => json.encode(data.toJson());

class FcmPass {
  bool? status;
  String? message;

  FcmPass({
    this.status,
    this.message,
  });

  factory FcmPass.fromJson(Map<String, dynamic> json) => FcmPass(
        status: json["status"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
      };
}
