import 'dart:convert';

WithoutLoginModel withoutLoginModelFromJson(String str) =>
    WithoutLoginModel.fromJson(json.decode(str));

String withoutLoginModelToJson(WithoutLoginModel data) =>
    json.encode(data.toJson());

class WithoutLoginModel {
  bool? status;
  String? message;
  Data? data;

  WithoutLoginModel({
    this.status,
    this.message,
    this.data,
  });

  factory WithoutLoginModel.fromJson(Map<String, dynamic> json) =>
      WithoutLoginModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  String? token;
  String? refreshToken;
  String? userId;
  String? role;
  String? expiresIn;
  String? vimeoId;
  String? message;
  String? versionIos;
  String? versionAndroid;

  Data({
    this.token,
    this.refreshToken,
    this.userId,
    this.role,
    this.expiresIn,
    this.vimeoId,
    this.message,
    this.versionIos,
    this.versionAndroid,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        token: json["token"],
        refreshToken: json["refreshToken"],
        userId: json["userId"],
        role: json["role"],
        expiresIn: json["expiresIn"],
        vimeoId: json["vimeoId"],
        message: json["message"],
        versionIos: json["version_ios"],
        versionAndroid: json["version_android"],
      );

  Map<String, dynamic> toJson() => {
        "token": token,
        "refreshToken": refreshToken,
        "userId": userId,
        "role": role,
        "expiresIn": expiresIn,
        "vimeoId": vimeoId,
        "message": message,
        "version_ios": versionIos,
        "version_android": versionAndroid,
      };
}
