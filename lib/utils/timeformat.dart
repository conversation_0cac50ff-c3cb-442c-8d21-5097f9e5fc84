import 'package:intl/intl.dart';

String timeAgo(DateTime dateTime) {
  final now = DateTime.now().toUtc();
  final difference = now.difference(dateTime);
  if (difference.inDays > 30) {
    final months = (difference.inDays / 30).floor();
    return '$months month${months > 1 ? 's' : ''} ago';
  }
  if (difference.inDays > 0) {
    final days = difference.inDays;
    return '$days day${days > 1 ? 's' : ''} ago';
  }
  if (difference.inHours > 0) {
    final hours = difference.inHours;
    return '$hours hour${hours > 1 ? 's' : ''} ago';
  }
  if (difference.inMinutes > 0) {
    final minutes = difference.inMinutes;
    return '$minutes minute${minutes > 1 ? 's' : ''} ago';
  }
  return 'just now';
}

String formatDate(String dateString) {
  DateTime dateTime = DateTime.parse(dateString).toLocal();
  DateFormat format = DateFormat('EEEE, d MMM yyyy');
  String formattedDate = format.format(dateTime);
  return formattedDate;
}

String formatTimeRange(String startDateString, String endDateString) {
  DateTime startDate = DateTime.parse(startDateString).toLocal();
  DateTime endDate = DateTime.parse(endDateString).toLocal();
  DateFormat format = DateFormat('h:mm a');
  String startTime = format.format(startDate);
  String endTime = format.format(endDate);
  return '$startTime - $endTime';
}

String formatTimeOnly(String dateString) {
  DateTime dateTime = DateTime.parse(dateString);
  DateFormat format = DateFormat('h:mm a');
  String formattedTime = format.format(dateTime);
  return formattedTime;
}

String dayChanger(String data) {
  // Parse the API response string into a DateTime object
  DateTime dateTime = DateTime.parse(data);

  // Format the DateTime object into a month date year format
  String formattedDate = DateFormat.yMMMMd().format(dateTime);

  print(formattedDate);
  return formattedDate; // Output: "April 17, 2023"
}

String formatDateTime(String? dateString) {
  if (dateString == null) {
    return '';
  }
  DateTime dateTime = DateTime.parse(dateString);
  DateFormat format = DateFormat('MM/dd/yyyy, hh:mm a');
  String formattedDateTime = format.format(dateTime);
  return formattedDateTime;
}

String formatDateTimeMessage(String dateTimeString) {
  final dateTime = DateTime.parse(dateTimeString).toLocal();
  final formatter = DateFormat("MM/dd/yyyy, hh:mm a");
  return formatter.format(dateTime);
}

String convertUtcToLocalTime(String utcTimeString) {
  DateTime utcTime = DateTime.parse(utcTimeString);
  DateTime localTime = utcTime.toLocal();
  String formattedTime = DateFormat.jm().format(localTime);

  return formattedTime;
}

String formatTimestamp(String timestamp) {
  DateTime dateTime = DateTime.parse(timestamp);
  String formattedDate = DateFormat('MM/dd/yyyy').format(dateTime);
  String formattedTime = DateFormat('hh:mm a').format(dateTime);

  return '$formattedDate, $formattedTime';
}

timeChange(String timestamp) {
  // Given timestamp

  DateTime originalDate = DateTime.parse(timestamp).toLocal();

  // Define the desired format
  DateFormat desiredFormat = DateFormat('MM/dd/yyyy, hh:mm a');

  // Format the date according to the desired format
  String formattedDate = desiredFormat.format(originalDate);
  return formattedDate;

  print(formattedDate); // Output: 06/09/2023, 09:08
}
