import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';

import 'appcolors.dart';

class AppProgress {
  static Center show() {
    return Center(
      child: CircularProgressIndicator(color: AppColors.colorPrimary),
    );
  }
}

class AppLoader extends StatelessWidget {
  const AppLoader({required this.child, Key? key}) : super(key: key);

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return LoaderOverlay(
      overlayColor: Colors.black.withOpacity(.6),
      overlayWidget: Center(
          child: CircularProgressIndicator(
        color: AppColors.colorPrimary,
      )),
      disableBackButton: false,
      useDefaultLoading: false,
      child: child,
    );
  }
}
