import 'dart:math';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/common_widgets/notificationhandle.dart';
import 'package:legacy/common_widgets/sidedrawer/cubit/sidemenu_cubit.dart';
import 'package:legacy/common_widgets/videoplayer/cubit/videoplayer_cubit.dart';
import 'package:legacy/features/announcements/screens/announcement.dart';
import 'package:legacy/features/announcements/cubit/announcements_cubit.dart';
import 'package:legacy/features/articles/articlesscreen.dart';
import 'package:legacy/features/articles/cubit/articles_cubit.dart';
import 'package:legacy/features/channels/channels.dart';
import 'package:legacy/features/channels/cubit/channel_cubit.dart';
import 'package:legacy/features/channels/cubit/cubit/channellist_cubit.dart';
import 'package:legacy/features/events/blocs/events_cubit.dart';
import 'package:legacy/features/events/events_screen.dart';
import 'package:legacy/features/homepage/cubit/home_cubit.dart';
import 'package:legacy/features/homepage/home.dart';
import 'package:legacy/features/loginpage/loginpage.dart';
import 'package:legacy/features/message/cubit/messageboard_cubit.dart';
import 'package:legacy/features/message/messagescreen.dart';
import 'package:legacy/features/podcasts/cubit/podcastlist/cubit/podcastlist_cubit.dart';
import 'package:legacy/features/podcasts/cubit/podcasts_cubit.dart';
import 'package:legacy/features/podcasts/screens/podcast.dart';
import 'package:legacy/features/splashscreen/splashscreen.dart';
import 'package:legacy/utils/app_pref.dart';
import 'package:legacy/utils/appcolors.dart';
import 'package:legacy/utils/images.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';

import 'common_widgets/controllers/network_controller.dart';
import 'common_widgets/sidedrawer/side_drawer.dart';
import 'common_widgets/tools.dart';
import 'features/announcements/cubit/announcedetail_cubit.dart';
import 'features/articles/cubit/articledetail_cubit.dart';
import 'features/message/cubit/cubit/messagedetails_cubit.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';

import 'package:platform_device_id/platform_device_id.dart';

PersistentTabController controller = PersistentTabController(initialIndex: 0);

bool pageload = false;
ValueNotifier<bool> bgNotificationReceived = ValueNotifier(false);
ValueNotifier<String?> pageMove = ValueNotifier(null);

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
@pragma('vm:entry-point')
Future _firebaseMessagingBackgroundHandler(message) async {
  bgNotificationReceived.value = true;

  pageload = true;
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  AwesomeNotifications().createNotification(
    content: NotificationContent(
      id: Random().nextInt(1000),
      channelKey: 'church',
      title: message.data['title'],
      body: message.data['message'],
      actionType: ActionType.Default,
      summary: message.data['type'],
    ),
  );
  print('Handling a background message ${message.messageId}');
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    systemNavigationBarColor: Colors.black,
    // transparent status bar
  ));

  await AppPref.load();
  await AwesomeNotifications().initialize(
      null,
      [
        NotificationChannel(
            importance: NotificationImportance.High,
            channelKey: 'church',
            channelName: 'church',
            channelDescription: 'be peaceful')
      ],
      debug: false);
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  FirebaseNotification().permissions;

  FirebaseNotification().fcm();
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  print('token =${AppPref.getString('token')}');
  runApp(MultiBlocProvider(providers: [
    BlocProvider(
      create: (context) => HomeCubit(),
    ),
    BlocProvider(
      create: (context) => ChannelCubit(),
    ),
    BlocProvider(
      create: (context) => PodcastsCubit(),
    ),
    BlocProvider(
      create: (context) => ChannellistCubit(),
    ),
    BlocProvider(
      create: (context) => AnnouncementsCubit(),
    ),
    BlocProvider(
      create: (context) => podcastlistCubit(),
    ),
    BlocProvider(
      create: (context) => VideoplayerCubit(),
    ),
    BlocProvider(
      create: (context) => ChannelCubit(),
    ),
    BlocProvider(
      create: (context) => PodcastsCubit(),
    ),
    BlocProvider(
      create: (context) => ChannellistCubit(),
    ),
    BlocProvider(
      create: (context) => AnnouncementsCubit(),
    ),
    BlocProvider(
      create: (context) => podcastlistCubit(),
    ),
    BlocProvider(
      create: (context) => EventsCubit(),
    ),
    BlocProvider(
      create: (context) => ArticlesCubit(),
    ),
    BlocProvider(
      create: (context) => AnnouncedetailCubit(),
    ),
    BlocProvider(create: (context) => ArticledetailCubit()),
    BlocProvider(create: (context) => MessageboardCubit()),
    BlocProvider(create: (context) => MessagedetailsCubit()),
    BlocProvider(
      create: (context) => SidemenuCubit(),
    ),

    // ignore: prefer_const_constructors
  ], child: MyApp()));
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  // ignore: prefer_typing_uninitialized_variables
  var homeScreen;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    AwesomeNotifications().setListeners(
      onActionReceivedMethod: NotificationController.onActionReceivedMethod,
    );
    hai();
    dev_id();
    NetworkController().loginApp(
      '${AppPref.getString('deviceId')}',
      '${AppPref.getString('usr_id')}',
      '${AppPref.getString('fcmToken')}',
    );
    print('${pageMove.value}-----------------------------value1');
    print('${bgNotificationReceived.value}-----------------------------value2');
  }

  hai() async {
    // await Permission.location.request();
    await Permission.notification.request();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      initialRoute: '/',
      routes: {
        // When navigating to the "/" route, build the FirstScreen widget.
        '/': (context) => const Splash(),
        // When navigating to the "/second" route, build the SecondScreen widget.
        '/msgboard': (context) => const MessageScreen(),
        '/login': (context) => const LoginPage(),
        '/MyApp': (context) => const MyApp(),
        '/channels': (context) => const channels(),

        '/podcast': (context) => const Podcasts(),
        '/article': (context) => const Articlespage(),
        '/event': (context) => const EventsScreen(),
        '/notification': (context) => const Announcements(),
        '/home': (context) => const MainHome(),
      },
      navigatorKey: navigatorKey,
      debugShowCheckedModeBanner: false,
      // home: homeScreen,
      theme: ThemeData(),
    );
  }
}

class MainHome extends StatefulWidget {
  const MainHome({super.key});

  @override
  State<MainHome> createState() => _MainHomeState();
}

class _MainHomeState extends State<MainHome> {
  bool drawerOpen = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    controller = PersistentTabController(initialIndex: 0);
    // AwesomeNotifications().isNotificationAllowed().then((isAllow) {
    //   if (!isAllow) {
    //     AwesomeNotifications().requestPermissionToSendNotifications();
    //   }
    // });
    FirebaseNotification().foregroundnotification(context);
    // navigate();
    notificationhalf();
    Timer(Duration(seconds: 2), () {
      nav();
    });
  }

  notificationhalf() {
    NotificationController.bgStream.listen((event) {
      if (event == "/notification") {
        controller.jumpToTab(3);
      }
      if (event == "/podcast") {
        controller.jumpToTab(2);
      }
      if (event == "/channels") {
        controller.jumpToTab(1);
      }
      if (event == "/article") {
        pushNewScreen(
          context,
          screen: const Articlespage(),
          withNavBar: false, // OPTIONAL VALUE. True by default.
          pageTransitionAnimation: PageTransitionAnimation.cupertino,
        );
      }
      if (event == "/event") {
        pushNewScreen(
          context,
          screen: const EventsScreen(),
          withNavBar: false, // OPTIONAL VALUE. True by default.
          pageTransitionAnimation: PageTransitionAnimation.cupertino,
        );
      }
      if (event == "/msgboard") {
        pushNewScreen(
          context,
          screen: const MessageScreen(),
          withNavBar: false, // OPTIONAL VALUE. True by default.
          pageTransitionAnimation: PageTransitionAnimation.cupertino,
        );
      }
    });
  }

  nav() {
    if (pageMove.value != null) {
      if (pageMove.value == "/notification") {
        pageMove.value = null;
        controller.jumpToTab(3);
      }
      if (pageMove.value == "/podcast") {
        pageMove.value = null;
        controller.jumpToTab(2);
      }
      if (pageMove.value == "/channels") {
        pageMove.value = null;
        controller.jumpToTab(1);
      }
      if (pageMove.value == "/article") {
        pageMove.value = null;
        pushNewScreen(
          context,
          screen: const Articlespage(),
          withNavBar: false, // OPTIONAL VALUE. True by default.
          pageTransitionAnimation: PageTransitionAnimation.cupertino,
        );
      }
      if (pageMove.value == "/event") {
        pageMove.value = null;

        pushNewScreen(
          context,
          screen: EventsScreen(),
          withNavBar: false, // OPTIONAL VALUE. True by default.
          pageTransitionAnimation: PageTransitionAnimation.cupertino,
        );
      }
      if (pageMove.value == "/msgboard") {
        pageMove.value = null;

        pushNewScreen(
          context,
          screen: MessageScreen(),
          withNavBar: false, // OPTIONAL VALUE. True by default.
          pageTransitionAnimation: PageTransitionAnimation.cupertino,
        );
      }
    }
  }
  // NotificationController.bgStream.listen((event) {
  //     if(event == "/notification"){
  //       controller.jumpToTab(3);
  //     }
  //   });

  navigate() {
    Timer(Duration(seconds: 3), () {
      if (bgNotificationReceived.value == true && pageMove.value != null) {
        alertmsg('-----------msg------------', context);
        print('onnnnnnnnnn--------------1122');
        if (pageMove.value == '1') {
          // Navigator.pushNamed(context,'/notification');
          //      pushNewScreen(
          //   context,
          //   screen: Announcements(),
          //   withNavBar: true, // OPTIONAL VALUE. True by default.
          //   pageTransitionAnimation: PageTransitionAnimation.cupertino,
          // );
          controller.jumpToTab(3);
          bgNotificationReceived.value = false;
        } else if (pageMove.value == '2') {
          pushNewScreen(
            context,
            screen: MessageScreen(),
            withNavBar: false, // OPTIONAL VALUE. True by default.
            pageTransitionAnimation: PageTransitionAnimation.cupertino,
          );
          bgNotificationReceived.value = false;
        } else if (pageMove.value == '3') {
          pushNewScreen(
            context,
            screen: Articlespage(),
            withNavBar: false, // OPTIONAL VALUE. True by default.
            pageTransitionAnimation: PageTransitionAnimation.cupertino,
          );
          // bgNotificationReceived.value = false;

          bgNotificationReceived.value = false;
        } else if (pageMove.value == '4') {
          pushNewScreen(
            context,
            screen: EventsScreen(),
            withNavBar: false, // OPTIONAL VALUE. True by default.
            pageTransitionAnimation: PageTransitionAnimation.cupertino,
          );
          bgNotificationReceived.value = false;
        } else if (pageMove.value == '5') {
          controller.jumpToTab(2);
          bgNotificationReceived.value = false;
        } else if (pageMove.value == '6') {
          controller.jumpToTab(1);
          bgNotificationReceived.value = false;
        }

        // pushNewScreen(
        //                         context,
        //                         screen: Announcements(),
        //                         withNavBar:
        //                             true, // OPTIONAL VALUE. True by default.
        //                         pageTransitionAnimation:
        //                             PageTransitionAnimation.cupertino,
        //                       );
// navigatorKey.currentState?.pushNamedAndRemoveUntil(
//           pageMove.value!, (route) => route.settings.name != pageMove.value);
      }
    });
  }

  final GlobalKey<ScaffoldState> _key = GlobalKey();
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  int selectedindex = 0;
  // ignore: no_leading_underscores_for_local_identifiers
  List<Widget> _buildScreens() {
    return [
      const HomePage(),
      const channels(),
      const Podcasts(),
      const Announcements()
    ];
  }

  List<PersistentBottomNavBarItem> _navBarsItems() {
    return [
      PersistentBottomNavBarItem(
        icon: SizedBox(height: 22, child: Image.asset(Images.bottomhomeselect)),
        inactiveIcon: SizedBox(
            height: 22,
            child: Image.asset(
              Images.bottomhomeunselect,
            )),
        title: "Home",
        activeColorPrimary: Colors.black,
        inactiveColorPrimary: CupertinoColors.systemGrey,
      ),
      PersistentBottomNavBarItem(
        icon: SizedBox(
            height: 22, child: Image.asset(Images.bottomchannelselect)),
        inactiveIcon: SizedBox(
            height: 22,
            child: Image.asset(
              Images.bottomchannelunselect,
            )),
        title: "Channels",
        activeColorPrimary: Colors.black,
        inactiveColorPrimary: CupertinoColors.systemGrey,
      ),
      PersistentBottomNavBarItem(
        icon: SizedBox(
            height: 22, child: Image.asset(Images.bottompodcastselect)),
        inactiveIcon: SizedBox(
            height: 22,
            child: Image.asset(
              Images.bottompodcastunselect,
            )),
        title: "Podcasts",
        activeColorPrimary: Colors.black,
        inactiveColorPrimary: CupertinoColors.systemGrey,
      ),
      PersistentBottomNavBarItem(
        icon: SizedBox(
            height: 22, child: Image.asset(Images.bottomannounceselect)),
        inactiveIcon: SizedBox(
            height: 22,
            child: Image.asset(
              Images.bottomannounceunselect,
            )),
        title: "Announcements",
        activeColorPrimary: Colors.black,
        inactiveColorPrimary: CupertinoColors.systemGrey,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    print('token -----------=${AppPref.getString('token')}');
    print('token ---status--------=${AppPref.getString('statuslogin')}');

    return PersistentTabView(
      context,
      controller: controller,
      // navBarHeight:scaffoldKey.currentState?.isDrawerOpen==true?0.0:56.0 ,
      screens: _buildScreens(),
      hideNavigationBar: drawerOpen,
      items: _navBarsItems(),
      confineInSafeArea: true,
      backgroundColor: AppColors.colorPrimary, // Default is Colors.white.
      handleAndroidBackButtonPress: true, // Default is true.
      resizeToAvoidBottomInset:
          true, // This needs to be true if you want to move up the screen when keyboard appears. Default is true.
      // popAllScreensOnTapAnyTabs: true,
      stateManagement: false, // Default is true.
      hideNavigationBarWhenKeyboardShows:
          true, // Recommended to set 'resizeToAvoidBottomInset' as true while using this argument. Default is true.
      // decoration: NavBarDecoration(
      //   borderRadius: BorderRadius.circular(10.0),
      //   colorBehindNavBar: Colors.white,
      // ),
      // popAllScreensOnTapOfSelectedTab: true,
      popActionScreens: PopActionScreensType.all,
      // itemAnimationProperties: const ItemAnimationProperties(
      //   // Navigation Bar's items animation properties.
      //   duration: Duration(milliseconds: 200),
      //   curve: Curves.ease,
      // ),

      screenTransitionAnimation: const ScreenTransitionAnimation(
        // Screen transition animation on change of selected tab.
        animateTabTransition: true,
        curve: Curves.ease,
        duration: Duration(milliseconds: 10),
      ),
      navBarStyle:
          NavBarStyle.simple, // Choose the nav bar style with this property.
    );
  }
}
