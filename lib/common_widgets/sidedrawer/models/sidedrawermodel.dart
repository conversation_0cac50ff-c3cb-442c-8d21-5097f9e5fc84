// To parse this JSON data, do
//
//     final sideDrawerModel = sideDrawerModelFromJson(jsonString);

import 'dart:convert';

SideDrawerModel sideDrawerModelFromJson(String str) =>
    SideDrawerModel.fromJson(json.decode(str));

String sideDrawerModelToJson(SideDrawerModel data) =>
    json.encode(data.toJson());

class SideDrawerModel {
  bool? status;
  String? message;
  Data? data;

  SideDrawerModel({
    this.status,
    this.message,
    this.data,
  });

  factory SideDrawerModel.fromJson(Map<String, dynamic> json) =>
      SideDrawerModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  int? articleUnreadCount;
  int? notificationUnreadCount;

  Data({
    this.articleUnreadCount,
    this.notificationUnreadCount,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        articleUnreadCount: json["articleUnreadCount"],
        notificationUnreadCount: json["notificationUnreadCount"],
      );

  Map<String, dynamic> toJson() => {
        "articleUnreadCount": articleUnreadCount,
        "notificationUnreadCount": notificationUnreadCount,
      };
}
