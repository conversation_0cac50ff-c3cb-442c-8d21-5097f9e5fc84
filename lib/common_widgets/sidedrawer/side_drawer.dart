import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/common_widgets/sidedrawer/cubit/sidemenu_cubit.dart';
import 'package:legacy/common_widgets/tools.dart';
import 'package:legacy/features/announcements/screens/announcement.dart';
import 'package:legacy/features/channels/channels.dart';
import 'package:legacy/features/events/events_screen.dart';
import 'package:legacy/features/homepage/home.dart';
import 'package:legacy/features/message/messagescreen.dart';
import 'package:legacy/utils/app_pref.dart';
import 'package:legacy/utils/app_styles.dart';
import 'package:legacy/utils/appcolors.dart';
import 'package:legacy/utils/gaps.dart';
import 'package:legacy/utils/images.dart';
import 'package:legacy/utils/models/logoutmodel.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent-tab-view.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../features/articles/articlesscreen.dart';
import '../../features/loginpage/loginpage.dart';
import '../../features/podcasts/screens/podcast.dart';
import '../../main.dart';
import '../controllers/network_controller.dart';
import 'models/sidedrawermodel.dart';

class DrawerWidget extends StatefulWidget {
  const DrawerWidget({super.key, this.drawerOpened});

  final Function? drawerOpened;

  @override
  State<DrawerWidget> createState() => _DrawerWidgetState();
}

class _DrawerWidgetState extends State<DrawerWidget> {
  final _navigatorKey = GlobalKey<NavigatorState>();
  SideDrawerModel? sidebar;

  @override
  void initState() {
    // TODO: implement initState

    super.initState();
    context.read<SidemenuCubit>().sideDrawerLoad();
  }

  final GlobalKey<ScaffoldState> _key = GlobalKey();

  @override
  Widget build(BuildContext context) {
    widget.drawerOpened;
    return BlocConsumer<SidemenuCubit, SidemenuState>(
        listener: (context, state) {
      if (state is Loaded) {
        print('55555555${state.sidedetails!.data}');
        setState(() {
          sidebar = state.sidedetails;
        });
      }
      // TODO: implement listener
    }, builder: (context, state) {
      return Scaffold(
        body: Container(
          width: double.infinity,
          color: AppColors.colorPrimary,
          child: Column(children: <Widget>[
            gapH32,
            gapH32,

            Row(
              children: [
                const Spacer(),
                IconButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  icon: const CircleAvatar(
                    backgroundColor: Color.fromARGB(255, 227, 217, 217),
                    radius: 12,
                    child: Icon(
                      Icons.close_rounded,
                      color: Colors.black,
                      size: 16,
                    ),
                  ),
                )
              ],
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: const [
                CircleAvatar(
                  radius: 40,
                  backgroundImage: AssetImage('assets/images/profilebg.png'),
                )
              ],
            ),
            gapH8,
            Align(
                alignment: Alignment.center,
                child: AppPref.getBool('loggedIn') == true
                    ? Text(
                        '${AppPref.getString('usr')}',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 15),
                      )
                    : Text('')),
            gapH12,
            // AppBar(automaticallyImplyLeading: false, title: Text('menu')),

            // Padding(
            //   padding: const EdgeInsets.all(15.0),
            //   child: SizedBox(
            //       height: 400,
            //       child: ListView.builder(
            //         physics: const NeverScrollableScrollPhysics(),
            //         itemBuilder: (BuildContext context, index) {
            //           return Container(
            //             height: 50,
            //             color: sideDrawerColor[index],
            //             child: Center(
            //               child: ListTile(contentPadding: EdgeInsets.only(left: 40),horizontalTitleGap:32.0,
            //                 leading: SizedBox(
            //                     height: 35,
            //                     width: 35,
            //                     child: Image.asset(sideDrawerIcons[index])),
            //                 title: Text(
            //                   sideDrawerNames[index],
            //                   style: const TextStyle(color: Colors.white),
            //                 ),
            //                 onTap: () {},
            //               ),
            //             ),
            //           );
            //         },
            //         itemCount: 7,
            //       )),

            Expanded(
                child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: SingleChildScrollView(
                child: SizedBox(
                  child: Column(children: [
                    Container(
                      color: Colors.black.withOpacity(0.2),
                      child: ListTile(
                        contentPadding: const EdgeInsets.only(left: 40),
                        horizontalTitleGap: 32.0,
                        leading: SizedBox(
                            height: 30,
                            width: 30,
                            child: Image.asset(Images.drawerhome)),
                        title: Text(
                          'Home',
                          style: textstyle4,
                        ),
                        onTap: () {
                          Navigator.of(context).pop();

                          // Navigator.of(context).push(MaterialPageRoute(
                          //     builder: (BuildContext context) =>
                          //         const MainHome()));
                          // pushNewScreen(
                          //   context,
                          //   screen: const HomePage(),
                          //   withNavBar:
                          //       true, // OPTIONAL VALUE. True by default.
                          //   pageTransitionAnimation:
                          //       PageTransitionAnimation.cupertino,
                          // );
                          controller.jumpToTab(0);
                        },
                      ),
                    ),
                    Container(
                      color: Colors.black.withOpacity(0.4),
                      child: ListTile(
                        contentPadding: const EdgeInsets.only(left: 40),
                        horizontalTitleGap: 32.0,
                        leading: SizedBox(
                            height: 30,
                            width: 30,
                            child: Image.asset(Images.drawerarticle)),
                        title: Text(
                          'Articles',
                          style: textstyle4,
                        ),
                        trailing: sidebar?.data?.articleUnreadCount != 0 &&
                                sidebar?.data != null
                            ? Padding(
                                padding: const EdgeInsets.only(right: 15),
                                child: Container(
                                  height: 25,
                                  width: 25,
                                  decoration: BoxDecoration(
                                      color: Colors.red,
                                      borderRadius: BorderRadius.circular(18)),
                                  child: Center(
                                      child: Text(
                                    sidebar?.data?.articleUnreadCount
                                            .toString() ??
                                        '',
                                    style: textstylenotification,
                                  )),
                                ),
                              )
                            : Text(''),
                        onTap: () {
                          Navigator.of(context).pop();

                          pushNewScreen(
                            context,
                            screen: Articlespage(),
                            withNavBar:
                                false, // OPTIONAL VALUE. True by default.
                            pageTransitionAnimation:
                                PageTransitionAnimation.cupertino,
                          ).then((value) =>
                              context.read<SidemenuCubit>().sideDrawerLoad());
                        },
                      ),
                    ),
                    Container(
                      color: Colors.black.withOpacity(0.2),
                      child: ListTile(
                        contentPadding: const EdgeInsets.only(left: 40),
                        horizontalTitleGap: 32.0,
                        leading: SizedBox(
                            height: 30,
                            width: 30,
                            child: Image.asset(Images.drawermessage)),
                        title: Text(
                          'Message Board',
                          style: textstyle4,
                        ),
                        onTap: () {
                          Navigator.of(context).pop();

                          // widget.drawerOpened;
                          pushNewScreen(
                            context,
                            screen: MessageScreen(),
                            withNavBar:
                                false, // OPTIONAL VALUE. True by default.
                            pageTransitionAnimation:
                                PageTransitionAnimation.cupertino,
                          );
                        },
                      ),
                    ),
                    Container(
                      color: Colors.black.withOpacity(0.4),
                      child: ListTile(
                        contentPadding: const EdgeInsets.only(left: 40),
                        horizontalTitleGap: 32.0,
                        leading: SizedBox(
                            height: 30,
                            width: 30,
                            child: Image.asset(Images.drawerevent)),
                        title: Text(
                          'Events',
                          style: textstyle4,
                        ),
                        onTap: () {
                          Navigator.of(context).pop();
                          pushNewScreen(
                            context,
                            screen: EventsScreen(),
                            withNavBar:
                                false, // OPTIONAL VALUE. True by default.
                            pageTransitionAnimation:
                                PageTransitionAnimation.cupertino,
                          );
                          // controller.jumpToTab(0);
                        },
                      ),
                    ),
                    Container(
                      color: Colors.black.withOpacity(0.2),
                      child: ListTile(
                        contentPadding: const EdgeInsets.only(left: 40),
                        horizontalTitleGap: 32.0,
                        leading: SizedBox(
                            height: 30,
                            width: 30,
                            child: Image.asset(Images.drawerchannel)),
                        title: Text(
                          'Channels',
                          style: textstyle4,
                        ),
                        onTap: () {
                          // _navigatorKey.currentState?.pushNamed('/channels');
                          // print('hiiiiiiiiiii');

                          //    pushNewScreen(
                          //   context,
                          //   screen: channels(),
                          //   withNavBar:
                          //       true, // OPTIONAL VALUE. True by default.
                          //   pageTransitionAnimation:
                          //       PageTransitionAnimation.cupertino,
                          // );
                          Navigator.of(context).pop();

                          controller.jumpToTab(1);

                          //                                 pushNewScreenWithRouteSettings(
                          // context,
                          // settings: RouteSettings(name:'/notification' ),
                          // screen:Announcements() ,
                          // withNavBar: true,
                          // pageTransitionAnimation: PageTransitionAnimation.cupertino,
                          // );
                          // Navigator.of(context).push(MaterialPageRoute(
                          //     builder: (BuildContext context) =>
                          //         const channels()));
                        },
                      ),
                    ),
                    Container(
                      color: Colors.black.withOpacity(0.4),
                      child: ListTile(
                        contentPadding: const EdgeInsets.only(left: 40),
                        horizontalTitleGap: 32.0,
                        leading: SizedBox(
                            height: 30,
                            width: 30,
                            child: Image.asset(Images.drawerpodcast)),
                        title: Text(
                          'Podcasts',
                          style: textstyle4,
                        ),
                        onTap: () {
                          Navigator.of(context).pop();

                          // Navigator.of(context).push(MaterialPageRoute(
                          //     builder: (BuildContext context) =>
                          //         const Podcasts()));
                          controller.jumpToTab(2);
                        },
                      ),
                    ),
                    Container(
                      color: Colors.black.withOpacity(0.2),
                      child: ListTile(
                        contentPadding: const EdgeInsets.only(left: 40),
                        horizontalTitleGap: 32.0,
                        leading: SizedBox(
                            height: 30,
                            width: 30,
                            child: Image.asset(Images.drawerannouncement)),
                        title: Text(
                          'Announcements',
                          style: textstyle4,
                        ),
                        trailing: sidebar?.data?.notificationUnreadCount != 0 &&
                                sidebar?.data != null
                            ? Padding(
                                padding: const EdgeInsets.only(right: 15),
                                child: Container(
                                  height: 25,
                                  width: 25,
                                  decoration: BoxDecoration(
                                      color: Colors.red,
                                      borderRadius: BorderRadius.circular(18)),
                                  child: Center(
                                      child: Text(
                                    sidebar?.data?.notificationUnreadCount
                                            .toString() ??
                                        '',
                                    style: textstylenotification,
                                  )),
                                ),
                              )
                            : Text(''),
                        onTap: () {
                          Navigator.of(context).pop();

                          // Navigator.of(context)
                          //     .push(MaterialPageRoute(
                          //         builder: (BuildContext context) =>
                          //             const Announcements()))
                          //     .then((value) => context
                          //         .read<SidemenuCubit>()
                          //         .sideDrawerLoad());
                          controller.jumpToTab(3);
                        },
                      ),
                    ),
                    AppPref.getBool('loggedIn') == true
                        ? Container(
                            color: Colors.black.withOpacity(0.4),
                            child: Center(
                              child: ListTile(
                                contentPadding: const EdgeInsets.only(left: 40),
                                horizontalTitleGap: 32.0,
                                leading: SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Image.asset(Images.drawerlogout),
                                ),
                                title:
                                    Text('Delete Account', style: textstyle4),
                                onTap: () async {
                                  showDialog<String>(
                                    context: context,
                                    builder: (BuildContext context) =>
                                        AlertDialog(
                                      title: const Text('Delete Account'),
                                      content: const Text(
                                          'Are you sure you want to delete account?'),
                                      actions: <Widget>[
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.pop(context, 'Cancel'),
                                          child: const Text(
                                            'Cancel',
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ),
                                        TextButton(
                                          onPressed: () async {
                                            try {
                                              var msg =
                                                  await NetworkController()
                                                      .deleteAccount();
                                              alertmsg(msg, context);
                                            } catch (e) {}

                                            SharedPreferences preferences =
                                                await SharedPreferences
                                                    .getInstance();
                                            await preferences.clear();

                                            Navigator.of(context)
                                                .pushNamedAndRemoveUntil(
                                                    '/login',
                                                    (Route<dynamic> route) =>
                                                        false);
                                          },
                                          child: const Text(
                                            'Delete Account',
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          )
                        : Text(''),
                    AppPref.getBool('loggedIn') == true
                        ? Container(
                            color: Colors.black.withOpacity(0.2),
                            child: Center(
                              child: ListTile(
                                contentPadding: const EdgeInsets.only(left: 40),
                                horizontalTitleGap: 32.0,
                                leading: SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Image.asset(Images.drawerlogout),
                                ),
                                title: Text('Logout', style: textstyle4),
                                onTap: () async {
                                  showDialog<String>(
                                    context: context,
                                    builder: (BuildContext context) =>
                                        AlertDialog(
                                      title: const Text('Logout'),
                                      content: const Text(
                                          'Are you sure you want to logout?'),
                                      actions: <Widget>[
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.pop(context, 'Cancel'),
                                          child: const Text(
                                            'Cancel',
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ),
                                        TextButton(
                                          onPressed: () async {
                                            try {
                                              String msg =
                                                  await NetworkController()
                                                      .logoutApp();
                                              alertmsg(msg, context);
                                            } catch (e) {}
                                            SharedPreferences preferences =
                                                await SharedPreferences
                                                    .getInstance();
                                            await preferences.clear();

                                            Navigator.of(context)
                                                .pushNamedAndRemoveUntil(
                                                    '/login',
                                                    (Route<dynamic> route) =>
                                                        false);
                                          },
                                          child: const Text(
                                            'Logout',
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          )
                        : Text('')
                  ]),
                ),
              ),
            )),
          ]),
        ),
      );
    });
  }
}
