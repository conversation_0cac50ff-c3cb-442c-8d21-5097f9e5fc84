part of 'sidemenu_cubit.dart';

@immutable
abstract class SidemenuState {}

class SidemenuInitial extends SidemenuState {}

class Loading extends SidemenuState {
  Loading();
}

class Loaded extends SidemenuState {
  final SideDrawerModel? sidedetails;

  Loaded({this.sidedetails});

  @override
  List<Object> get props => [];
}

class Message extends SidemenuState {
  final String message;

  Message(this.message);

  @override
  List<Object> get props => [];
}
