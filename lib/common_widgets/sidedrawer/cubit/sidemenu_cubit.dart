import 'package:bloc/bloc.dart';
import 'package:legacy/common_widgets/sidedrawer/models/sidedrawermodel.dart';
import 'package:meta/meta.dart';

import '../../../utils/app_pref.dart';
import '../../controllers/network_controller.dart';

part 'sidemenu_state.dart';

class SidemenuCubit extends Cubit<SidemenuState> {
  SidemenuCubit() : super(SidemenuInitial());
  Future<void> sideDrawerLoad() async {
    emit(Loading());
    String? token = AppPref.getString('token');
    print('qwer$token');

    emit(Loading());
    try {
      SideDrawerModel body = await NetworkController().sidedrawer('$token');

// alertmsg('${AppPref.getString('msg')}');
      print('333333');

      emit(Loaded(sidedetails: body));
    } catch (e) {
      emit(Message(e.toString()));
      print('411444444${e.toString()}');
    }
  }
}
