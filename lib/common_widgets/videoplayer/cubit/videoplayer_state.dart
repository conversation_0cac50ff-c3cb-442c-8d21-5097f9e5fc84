part of 'videoplayer_cubit.dart';

abstract class VideoplayerState {}

class VideoplayerInitial extends VideoplayerState {}

class Loading extends VideoplayerState {
  Loading();
}

class Loaded extends VideoplayerState {
  final VideoPlayerModel? videodetails;

  Loaded({this.videodetails});

  @override
  List<Object> get props => [];
}

class Message extends VideoplayerState {
  final String message;

  Message(this.message);

  @override
  List<Object> get props => [];
}
