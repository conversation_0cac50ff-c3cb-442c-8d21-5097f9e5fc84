import 'package:bloc/bloc.dart';
import 'package:legacy/common_widgets/videoplayer/models/videoplayermodels.dart';

import '../../../utils/app_pref.dart';
import '../../controllers/network_controller.dart';

part 'videoplayer_state.dart';

class VideoplayerCubit extends Cubit<VideoplayerState> {
  VideoplayerCubit() : super(VideoplayerInitial());

  Future<void> recentvideoplay() async {
    emit(Loading());
    String? token = AppPref.getString('vimeo_id');
    String? vimeoId = AppPref.getString('vimeoId');
    print('qwer$token');

    emit(Loading());
    try {
      print('object1212');
      VideoPlayerModel body =
          await NetworkController().getvideo('$token', '$vimeoId');
      print('78787878-------$body');

      emit(Loaded(videodetails: body));
      print('123123123');
    } catch (e) {
      emit(Message(e.toString()));
      print('4111144${e.toString()}');
    }
  }
}
