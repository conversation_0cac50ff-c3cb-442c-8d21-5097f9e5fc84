import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:legacy/common_widgets/videoplayer/cubit/videoplayer_cubit.dart';
import 'package:legacy/common_widgets/videoplayer/models/videoplayermodels.dart'
    as kk;
import 'package:loader_overlay/loader_overlay.dart';
import 'package:video_player/video_player.dart';

import '../../utils/progress.dart';

class VideoPlayerPage extends StatefulWidget {
  const VideoPlayerPage({super.key});

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  kk.VideoPlayerModel? videolist;
  late VideoPlayerController playerController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<VideoplayerCubit>().recentvideoplay();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    playerController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    return AppLoader(
      child: BlocConsumer<VideoplayerCubit, VideoplayerState>(
        listener: (context, state) {
          if (state is Loaded) {
            print('22222222$state');
            print('55555555${state.videodetails}');
            setState(() {
              videolist = state.videodetails;
            });
            playerController = VideoPlayerController.network(
                state.videodetails?.files?[0].link ?? '');
          }
          // TODO: implement listener
        },
        builder: (context, state) {
          print('--11--22--$videolist');
          (state is Loading)
              ? context.loaderOverlay.show()
              : context.loaderOverlay.hide();
          if (state is Loaded) {
            return Scaffold(
              backgroundColor: Colors.black,
              appBar: AppBar(backgroundColor: Colors.black, iconTheme: IconThemeData(
    color: Colors.white, //change your color here
  ),
        ),
              body: ListView(
                children: [
                  SizedBox(height: MediaQuery.of(context).size.height / 4),
                  AspectRatio(
                    aspectRatio: 16 / 9,
                    child: Center(
                      child: ChewieListItem(
                        videoPlayerController: playerController,
                        looping: true,
                      ),
                    ),
                  ),
                ],
              ),
            );

            // ChewieListItem(
            //   videoPlayerController: VideoPlayerController.asset(
            //     'assets/images/videoplayback.mp4'
            //   ),
            // ),
            // ChewieListItem(
            //   // This URL doesn't exist - will display an error
            //   videoPlayerController: VideoPlayerController.network(
            //     'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/error.mp4',
            //   ),
            // ),
          }
          return Text('');
        },
      ),
    );
  }
}

class ChewieListItem extends StatefulWidget {
  // This will contain the URL/asset path which we want to play
  final VideoPlayerController videoPlayerController;
  final bool? looping;

  const ChewieListItem({
    required this.videoPlayerController,
    Key? key,
    this.looping,
  }) : super(key: key);

  @override
  _ChewieListItemState createState() => _ChewieListItemState();
}

class _ChewieListItemState extends State<ChewieListItem> {
  late ChewieController _chewieController;

  @override
  void initState() {
    super.initState();
    // Wrapper on top of the videoPlayerController
    _chewieController = ChewieController(
      autoPlay: true,
      allowPlaybackSpeedChanging: true,
      videoPlayerController: widget.videoPlayerController,
      aspectRatio: 16 / 9,
      // Prepare the video to be played and display the first frame
      autoInitialize: true, allowFullScreen: true,
      looping: widget.looping ?? false,
      // fullScreenByDefault: true,
      // Errors can occur for example when trying to play a video
      // from a non-existent URL
      errorBuilder: (context, errorMessage) {
        return Center(
          child: Text(
            errorMessage,
            style: const TextStyle(color: Colors.amberAccent),
          ),
        );
      },
    );
    // _chewieController.addListener(() {
    //   if (_chewieController.isFullScreen) {
    //     SystemChrome.setPreferredOrientations([
    //       DeviceOrientation.landscapeRight,
    //       DeviceOrientation.landscapeLeft,
    //     ]);
    //   } else {
    //     SystemChrome.setPreferredOrientations([
    //       DeviceOrientation.portraitUp,
    //       DeviceOrientation.portraitDown,
    //     ]);
    //   }
    // });
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    return WillPopScope(
      onWillPop: () {
        widget.videoPlayerController.dispose();
        _chewieController.dispose();
        return Future.value(true);
      },
      child: Chewie(
        controller: _chewieController,
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    // IMPORTANT to dispose of all the used resources
    widget.videoPlayerController.dispose();
    _chewieController.dispose();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }
}
