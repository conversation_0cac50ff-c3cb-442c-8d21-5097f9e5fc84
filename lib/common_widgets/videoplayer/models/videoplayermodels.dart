// To parse this JSON data, do
//
//     final videoPlayerModel = videoPlayerModelFromJson(jsonString);

import 'dart:convert';

VideoPlayerModel videoPlayerModelFromJson(String str) =>
    VideoPlayerModel.fromJson(json.decode(str));

String videoPlayerModelToJson(VideoPlayerModel data) =>
    json.encode(data.toJson());

class VideoPlayerModel {
  String? uri;
  String? name;
  String? description;
  String? type;
  String? link;
  String? playerEmbedUrl;
  num? duration;
  num? width;
  dynamic language;
  num? height;
  Embed? embed;
  DateTime? createdTime;
  DateTime? modifiedTime;
  DateTime? releaseTime;
  List<String>? contentRating;
  String? contentRatingClass;
  bool? ratingModLocked;
  dynamic license;
  Privacy? privacy;
  Pictures? pictures;
  List<dynamic>? tags;
  Stats? stats;
  List<dynamic>? categories;
  Uploader? uploader;
  VideoPlayerModelMetadata? metadata;
  String? manageLink;
  User? user;
  dynamic parentFolder;
  DateTime? lastUserActionEventDate;
  ReviewPage? reviewPage;
  List<Download>? files;
  List<Download>? download;
  Play? play;
  App? app;
  String? status;
  String? resourceKey;
  Upload? upload;
  Transcode? transcode;
  bool? isPlayable;
  bool? hasAudio;

  VideoPlayerModel({
    this.uri,
    this.name,
    this.description,
    this.type,
    this.link,
    this.playerEmbedUrl,
    this.duration,
    this.width,
    this.language,
    this.height,
    this.embed,
    this.createdTime,
    this.modifiedTime,
    this.releaseTime,
    this.contentRating,
    this.contentRatingClass,
    this.ratingModLocked,
    this.license,
    this.privacy,
    this.pictures,
    this.tags,
    this.stats,
    this.categories,
    this.uploader,
    this.metadata,
    this.manageLink,
    this.user,
    this.parentFolder,
    this.lastUserActionEventDate,
    this.reviewPage,
    this.files,
    this.download,
    this.play,
    this.app,
    this.status,
    this.resourceKey,
    this.upload,
    this.transcode,
    this.isPlayable,
    this.hasAudio,
  });

  factory VideoPlayerModel.fromJson(Map<String, dynamic> json) =>
      VideoPlayerModel(
        uri: json["uri"],
        name: json["name"],
        description: json["description"],
        type: json["type"],
        link: json["link"],
        playerEmbedUrl: json["player_embed_url"],
        duration: json["duration"],
        width: json["width"],
        language: json["language"],
        height: json["height"],
        embed: json["embed"] == null ? null : Embed.fromJson(json["embed"]),
        createdTime: json["created_time"] == null
            ? null
            : DateTime.parse(json["created_time"]),
        modifiedTime: json["modified_time"] == null
            ? null
            : DateTime.parse(json["modified_time"]),
        releaseTime: json["release_time"] == null
            ? null
            : DateTime.parse(json["release_time"]),
        contentRating: json["content_rating"] == null
            ? []
            : List<String>.from(json["content_rating"]!.map((x) => x)),
        contentRatingClass: json["content_rating_class"],
        ratingModLocked: json["rating_mod_locked"],
        license: json["license"],
        privacy:
            json["privacy"] == null ? null : Privacy.fromJson(json["privacy"]),
        pictures: json["pictures"] == null
            ? null
            : Pictures.fromJson(json["pictures"]),
        tags: json["tags"] == null
            ? []
            : List<dynamic>.from(json["tags"]!.map((x) => x)),
        stats: json["stats"] == null ? null : Stats.fromJson(json["stats"]),
        categories: json["categories"] == null
            ? []
            : List<dynamic>.from(json["categories"]!.map((x) => x)),
        uploader: json["uploader"] == null
            ? null
            : Uploader.fromJson(json["uploader"]),
        metadata: json["metadata"] == null
            ? null
            : VideoPlayerModelMetadata.fromJson(json["metadata"]),
        manageLink: json["manage_link"],
        user: json["user"] == null ? null : User.fromJson(json["user"]),
        parentFolder: json["parent_folder"],
        lastUserActionEventDate: json["last_user_action_event_date"] == null
            ? null
            : DateTime.parse(json["last_user_action_event_date"]),
        reviewPage: json["review_page"] == null
            ? null
            : ReviewPage.fromJson(json["review_page"]),
        files: json["files"] == null
            ? []
            : List<Download>.from(
                json["files"]!.map((x) => Download.fromJson(x))),
        download: json["download"] == null
            ? []
            : List<Download>.from(
                json["download"]!.map((x) => Download.fromJson(x))),
        play: json["play"] == null ? null : Play.fromJson(json["play"]),
        app: json["app"] == null ? null : App.fromJson(json["app"]),
        status: json["status"],
        resourceKey: json["resource_key"],
        upload: json["upload"] == null ? null : Upload.fromJson(json["upload"]),
        transcode: json["transcode"] == null
            ? null
            : Transcode.fromJson(json["transcode"]),
        isPlayable: json["is_playable"],
        hasAudio: json["has_audio"],
      );

  Map<String, dynamic> toJson() => {
        "uri": uri,
        "name": name,
        "description": description,
        "type": type,
        "link": link,
        "player_embed_url": playerEmbedUrl,
        "duration": duration,
        "width": width,
        "language": language,
        "height": height,
        "embed": embed?.toJson(),
        "created_time": createdTime?.toIso8601String(),
        "modified_time": modifiedTime?.toIso8601String(),
        "release_time": releaseTime?.toIso8601String(),
        "content_rating": contentRating == null
            ? []
            : List<dynamic>.from(contentRating!.map((x) => x)),
        "content_rating_class": contentRatingClass,
        "rating_mod_locked": ratingModLocked,
        "license": license,
        "privacy": privacy?.toJson(),
        "pictures": pictures?.toJson(),
        "tags": tags == null ? [] : List<dynamic>.from(tags!.map((x) => x)),
        "stats": stats?.toJson(),
        "categories": categories == null
            ? []
            : List<dynamic>.from(categories!.map((x) => x)),
        "uploader": uploader?.toJson(),
        "metadata": metadata?.toJson(),
        "manage_link": manageLink,
        "user": user?.toJson(),
        "parent_folder": parentFolder,
        "last_user_action_event_date":
            lastUserActionEventDate?.toIso8601String(),
        "review_page": reviewPage?.toJson(),
        "files": files == null
            ? []
            : List<dynamic>.from(files!.map((x) => x.toJson())),
        "download": download == null
            ? []
            : List<dynamic>.from(download!.map((x) => x.toJson())),
        "play": play?.toJson(),
        "app": app?.toJson(),
        "status": status,
        "resource_key": resourceKey,
        "upload": upload?.toJson(),
        "transcode": transcode?.toJson(),
        "is_playable": isPlayable,
        "has_audio": hasAudio,
      };
}

class App {
  String? name;
  String? uri;

  App({
    this.name,
    this.uri,
  });

  factory App.fromJson(Map<String, dynamic> json) => App(
        name: json["name"],
        uri: json["uri"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "uri": uri,
      };
}

class Download {
  String? quality;
  String? rendition;
  String? type;
  num? width;
  num? height;
  DateTime? expires;
  String? link;
  DateTime? createdTime;
  num? fps;
  num? size;
  String? md5;
  String? publicName;
  String? sizeShort;
  String? codec;
  DateTime? linkExpirationTime;

  Download({
    this.quality,
    this.rendition,
    this.type,
    this.width,
    this.height,
    this.expires,
    this.link,
    this.createdTime,
    this.fps,
    this.size,
    this.md5,
    this.publicName,
    this.sizeShort,
    this.codec,
    this.linkExpirationTime,
  });

  factory Download.fromJson(Map<String, dynamic> json) => Download(
        quality: json["quality"],
        rendition: json["rendition"],
        type: json["type"],
        width: json["width"],
        height: json["height"],
        expires:
            json["expires"] == null ? null : DateTime.parse(json["expires"]),
        link: json["link"],
        createdTime: json["created_time"] == null
            ? null
            : DateTime.parse(json["created_time"]),
        fps: json["fps"],
        size: json["size"],
        md5: json["md5"],
        publicName: json["public_name"],
        sizeShort: json["size_short"],
        codec: json["codec"],
        linkExpirationTime: json["link_expiration_time"] == null
            ? null
            : DateTime.parse(json["link_expiration_time"]),
      );

  Map<String, dynamic> toJson() => {
        "quality": quality,
        "rendition": rendition,
        "type": type,
        "width": width,
        "height": height,
        "expires": expires?.toIso8601String(),
        "link": link,
        "created_time": createdTime?.toIso8601String(),
        "fps": fps,
        "size": size,
        "md5": md5,
        "public_name": publicName,
        "size_short": sizeShort,
        "codec": codec,
        "link_expiration_time": linkExpirationTime?.toIso8601String(),
      };
}

class Embed {
  String? html;
  Badges? badges;
  Buttons? buttons;
  Logos? logos;
  Title? title;
  List<dynamic>? endScreen;
  bool? playbar;
  bool? pip;
  bool? autopip;
  bool? volume;
  String? color;
  Colors? colors;
  bool? eventSchedule;
  bool? interactive;
  bool? hasCards;
  String? outroType;
  bool? showTimezone;
  List<dynamic>? cards;
  dynamic uri;
  dynamic emailCaptureForm;
  bool? speed;

  Embed({
    this.html,
    this.badges,
    this.buttons,
    this.logos,
    this.title,
    this.endScreen,
    this.playbar,
    this.pip,
    this.autopip,
    this.volume,
    this.color,
    this.colors,
    this.eventSchedule,
    this.interactive,
    this.hasCards,
    this.outroType,
    this.showTimezone,
    this.cards,
    this.uri,
    this.emailCaptureForm,
    this.speed,
  });

  factory Embed.fromJson(Map<String, dynamic> json) => Embed(
        html: json["html"],
        badges: json["badges"] == null ? null : Badges.fromJson(json["badges"]),
        buttons:
            json["buttons"] == null ? null : Buttons.fromJson(json["buttons"]),
        logos: json["logos"] == null ? null : Logos.fromJson(json["logos"]),
        title: json["title"] == null ? null : Title.fromJson(json["title"]),
        endScreen: json["end_screen"] == null
            ? []
            : List<dynamic>.from(json["end_screen"]!.map((x) => x)),
        playbar: json["playbar"],
        pip: json["pip"],
        autopip: json["autopip"],
        volume: json["volume"],
        color: json["color"],
        colors: json["colors"] == null ? null : Colors.fromJson(json["colors"]),
        eventSchedule: json["event_schedule"],
        interactive: json["interactive"],
        hasCards: json["has_cards"],
        outroType: json["outro_type"],
        showTimezone: json["show_timezone"],
        cards: json["cards"] == null
            ? []
            : List<dynamic>.from(json["cards"]!.map((x) => x)),
        uri: json["uri"],
        emailCaptureForm: json["email_capture_form"],
        speed: json["speed"],
      );

  Map<String, dynamic> toJson() => {
        "html": html,
        "badges": badges?.toJson(),
        "buttons": buttons?.toJson(),
        "logos": logos?.toJson(),
        "title": title?.toJson(),
        "end_screen": endScreen == null
            ? []
            : List<dynamic>.from(endScreen!.map((x) => x)),
        "playbar": playbar,
        "pip": pip,
        "autopip": autopip,
        "volume": volume,
        "color": color,
        "colors": colors?.toJson(),
        "event_schedule": eventSchedule,
        "interactive": interactive,
        "has_cards": hasCards,
        "outro_type": outroType,
        "show_timezone": showTimezone,
        "cards": cards == null ? [] : List<dynamic>.from(cards!.map((x) => x)),
        "uri": uri,
        "email_capture_form": emailCaptureForm,
        "speed": speed,
      };
}

class Badges {
  bool? hdr;
  Live? live;
  StaffPick? staffPick;
  bool? vod;
  bool? weekendChallenge;

  Badges({
    this.hdr,
    this.live,
    this.staffPick,
    this.vod,
    this.weekendChallenge,
  });

  factory Badges.fromJson(Map<String, dynamic> json) => Badges(
        hdr: json["hdr"],
        live: json["live"] == null ? null : Live.fromJson(json["live"]),
        staffPick: json["staff_pick"] == null
            ? null
            : StaffPick.fromJson(json["staff_pick"]),
        vod: json["vod"],
        weekendChallenge: json["weekend_challenge"],
      );

  Map<String, dynamic> toJson() => {
        "hdr": hdr,
        "live": live?.toJson(),
        "staff_pick": staffPick?.toJson(),
        "vod": vod,
        "weekend_challenge": weekendChallenge,
      };
}

class Live {
  bool? streaming;
  bool? archived;

  Live({
    this.streaming,
    this.archived,
  });

  factory Live.fromJson(Map<String, dynamic> json) => Live(
        streaming: json["streaming"],
        archived: json["archived"],
      );

  Map<String, dynamic> toJson() => {
        "streaming": streaming,
        "archived": archived,
      };
}

class StaffPick {
  bool? normal;
  bool? bestOfTheMonth;
  bool? bestOfTheYear;
  bool? premiere;

  StaffPick({
    this.normal,
    this.bestOfTheMonth,
    this.bestOfTheYear,
    this.premiere,
  });

  factory StaffPick.fromJson(Map<String, dynamic> json) => StaffPick(
        normal: json["normal"],
        bestOfTheMonth: json["best_of_the_month"],
        bestOfTheYear: json["best_of_the_year"],
        premiere: json["premiere"],
      );

  Map<String, dynamic> toJson() => {
        "normal": normal,
        "best_of_the_month": bestOfTheMonth,
        "best_of_the_year": bestOfTheYear,
        "premiere": premiere,
      };
}

class Buttons {
  bool? watchlater;
  bool? share;
  bool? embed;
  bool? hd;
  bool? fullscreen;
  bool? scaling;
  bool? like;

  Buttons({
    this.watchlater,
    this.share,
    this.embed,
    this.hd,
    this.fullscreen,
    this.scaling,
    this.like,
  });

  factory Buttons.fromJson(Map<String, dynamic> json) => Buttons(
        watchlater: json["watchlater"],
        share: json["share"],
        embed: json["embed"],
        hd: json["hd"],
        fullscreen: json["fullscreen"],
        scaling: json["scaling"],
        like: json["like"],
      );

  Map<String, dynamic> toJson() => {
        "watchlater": watchlater,
        "share": share,
        "embed": embed,
        "hd": hd,
        "fullscreen": fullscreen,
        "scaling": scaling,
        "like": like,
      };
}

class Colors {
  String? colorOne;
  String? colorTwo;
  String? colorThree;
  String? colorFour;

  Colors({
    this.colorOne,
    this.colorTwo,
    this.colorThree,
    this.colorFour,
  });

  factory Colors.fromJson(Map<String, dynamic> json) => Colors(
        colorOne: json["color_one"],
        colorTwo: json["color_two"],
        colorThree: json["color_three"],
        colorFour: json["color_four"],
      );

  Map<String, dynamic> toJson() => {
        "color_one": colorOne,
        "color_two": colorTwo,
        "color_three": colorThree,
        "color_four": colorFour,
      };
}

class Logos {
  bool? vimeo;
  Custom? custom;

  Logos({
    this.vimeo,
    this.custom,
  });

  factory Logos.fromJson(Map<String, dynamic> json) => Logos(
        vimeo: json["vimeo"],
        custom: json["custom"] == null ? null : Custom.fromJson(json["custom"]),
      );

  Map<String, dynamic> toJson() => {
        "vimeo": vimeo,
        "custom": custom?.toJson(),
      };
}

class Custom {
  bool? active;
  dynamic url;
  dynamic link;
  bool? useLink;
  bool? sticky;

  Custom({
    this.active,
    this.url,
    this.link,
    this.useLink,
    this.sticky,
  });

  factory Custom.fromJson(Map<String, dynamic> json) => Custom(
        active: json["active"],
        url: json["url"],
        link: json["link"],
        useLink: json["use_link"],
        sticky: json["sticky"],
      );

  Map<String, dynamic> toJson() => {
        "active": active,
        "url": url,
        "link": link,
        "use_link": useLink,
        "sticky": sticky,
      };
}

class Title {
  String? name;
  String? owner;
  String? portrait;

  Title({
    this.name,
    this.owner,
    this.portrait,
  });

  factory Title.fromJson(Map<String, dynamic> json) => Title(
        name: json["name"],
        owner: json["owner"],
        portrait: json["portrait"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "owner": owner,
        "portrait": portrait,
      };
}

class VideoPlayerModelMetadata {
  PurpleConnections? connections;
  Interactions? interactions;
  bool? isVimeoCreate;
  bool? isScreenRecord;

  VideoPlayerModelMetadata({
    this.connections,
    this.interactions,
    this.isVimeoCreate,
    this.isScreenRecord,
  });

  factory VideoPlayerModelMetadata.fromJson(Map<String, dynamic> json) =>
      VideoPlayerModelMetadata(
        connections: json["connections"] == null
            ? null
            : PurpleConnections.fromJson(json["connections"]),
        interactions: json["interactions"] == null
            ? null
            : Interactions.fromJson(json["interactions"]),
        isVimeoCreate: json["is_vimeo_create"],
        isScreenRecord: json["is_screen_record"],
      );

  Map<String, dynamic> toJson() => {
        "connections": connections?.toJson(),
        "interactions": interactions?.toJson(),
        "is_vimeo_create": isVimeoCreate,
        "is_screen_record": isScreenRecord,
      };
}

class PurpleConnections {
  Albums? comments;
  Albums? credits;
  Albums? likes;
  Albums? pictures;
  Albums? texttracks;
  dynamic related;
  Recommendations? recommendations;
  Albums? albums;
  Albums? availableAlbums;
  Albums? availableChannels;
  Versions? versions;

  PurpleConnections({
    this.comments,
    this.credits,
    this.likes,
    this.pictures,
    this.texttracks,
    this.related,
    this.recommendations,
    this.albums,
    this.availableAlbums,
    this.availableChannels,
    this.versions,
  });

  factory PurpleConnections.fromJson(Map<String, dynamic> json) =>
      PurpleConnections(
        comments:
            json["comments"] == null ? null : Albums.fromJson(json["comments"]),
        credits:
            json["credits"] == null ? null : Albums.fromJson(json["credits"]),
        likes: json["likes"] == null ? null : Albums.fromJson(json["likes"]),
        pictures:
            json["pictures"] == null ? null : Albums.fromJson(json["pictures"]),
        texttracks: json["texttracks"] == null
            ? null
            : Albums.fromJson(json["texttracks"]),
        related: json["related"],
        recommendations: json["recommendations"] == null
            ? null
            : Recommendations.fromJson(json["recommendations"]),
        albums: json["albums"] == null ? null : Albums.fromJson(json["albums"]),
        availableAlbums: json["available_albums"] == null
            ? null
            : Albums.fromJson(json["available_albums"]),
        availableChannels: json["available_channels"] == null
            ? null
            : Albums.fromJson(json["available_channels"]),
        versions: json["versions"] == null
            ? null
            : Versions.fromJson(json["versions"]),
      );

  Map<String, dynamic> toJson() => {
        "comments": comments?.toJson(),
        "credits": credits?.toJson(),
        "likes": likes?.toJson(),
        "pictures": pictures?.toJson(),
        "texttracks": texttracks?.toJson(),
        "related": related,
        "recommendations": recommendations?.toJson(),
        "albums": albums?.toJson(),
        "available_albums": availableAlbums?.toJson(),
        "available_channels": availableChannels?.toJson(),
        "versions": versions?.toJson(),
      };
}

class Albums {
  String? uri;
  List<String>? options;
  int? total;

  Albums({
    this.uri,
    this.options,
    this.total,
  });

  factory Albums.fromJson(Map<String, dynamic> json) => Albums(
        uri: json["uri"],
        options: json["options"] == null
            ? []
            : List<String>.from(json["options"]!.map((x) => x)),
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "uri": uri,
        "options":
            options == null ? [] : List<dynamic>.from(options!.map((x) => x)),
        "total": total,
      };
}

class Recommendations {
  String? uri;
  List<String>? options;

  Recommendations({
    this.uri,
    this.options,
  });

  factory Recommendations.fromJson(Map<String, dynamic> json) =>
      Recommendations(
        uri: json["uri"],
        options: json["options"] == null
            ? []
            : List<String>.from(json["options"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "uri": uri,
        "options":
            options == null ? [] : List<dynamic>.from(options!.map((x) => x)),
      };
}

class Versions {
  String? uri;
  List<String>? options;
  int? total;
  String? currentUri;
  String? resourceKey;
  dynamic latestIncompleteVersion;

  Versions({
    this.uri,
    this.options,
    this.total,
    this.currentUri,
    this.resourceKey,
    this.latestIncompleteVersion,
  });

  factory Versions.fromJson(Map<String, dynamic> json) => Versions(
        uri: json["uri"],
        options: json["options"] == null
            ? []
            : List<String>.from(json["options"]!.map((x) => x)),
        total: json["total"],
        currentUri: json["current_uri"],
        resourceKey: json["resource_key"],
        latestIncompleteVersion: json["latest_incomplete_version"],
      );

  Map<String, dynamic> toJson() => {
        "uri": uri,
        "options":
            options == null ? [] : List<dynamic>.from(options!.map((x) => x)),
        "total": total,
        "current_uri": currentUri,
        "resource_key": resourceKey,
        "latest_incomplete_version": latestIncompleteVersion,
      };
}

class Interactions {
  Watchlater? watchlater;
  Report? report;
  Recommendations? viewTeamMembers;
  Edit? edit;
  EditContentRating? editContentRating;
  EditPrivacy? editPrivacy;
  Recommendations? delete;
  Recommendations? canUpdatePrivacyToPublic;
  Recommendations? trim;
  Recommendations? validate;

  Interactions({
    this.watchlater,
    this.report,
    this.viewTeamMembers,
    this.edit,
    this.editContentRating,
    this.editPrivacy,
    this.delete,
    this.canUpdatePrivacyToPublic,
    this.trim,
    this.validate,
  });

  factory Interactions.fromJson(Map<String, dynamic> json) => Interactions(
        watchlater: json["watchlater"] == null
            ? null
            : Watchlater.fromJson(json["watchlater"]),
        report: json["report"] == null ? null : Report.fromJson(json["report"]),
        viewTeamMembers: json["view_team_members"] == null
            ? null
            : Recommendations.fromJson(json["view_team_members"]),
        edit: json["edit"] == null ? null : Edit.fromJson(json["edit"]),
        editContentRating: json["edit_content_rating"] == null
            ? null
            : EditContentRating.fromJson(json["edit_content_rating"]),
        editPrivacy: json["edit_privacy"] == null
            ? null
            : EditPrivacy.fromJson(json["edit_privacy"]),
        delete: json["delete"] == null
            ? null
            : Recommendations.fromJson(json["delete"]),
        canUpdatePrivacyToPublic: json["can_update_privacy_to_public"] == null
            ? null
            : Recommendations.fromJson(json["can_update_privacy_to_public"]),
        trim: json["trim"] == null
            ? null
            : Recommendations.fromJson(json["trim"]),
        validate: json["validate"] == null
            ? null
            : Recommendations.fromJson(json["validate"]),
      );

  Map<String, dynamic> toJson() => {
        "watchlater": watchlater?.toJson(),
        "report": report?.toJson(),
        "view_team_members": viewTeamMembers?.toJson(),
        "edit": edit?.toJson(),
        "edit_content_rating": editContentRating?.toJson(),
        "edit_privacy": editPrivacy?.toJson(),
        "delete": delete?.toJson(),
        "can_update_privacy_to_public": canUpdatePrivacyToPublic?.toJson(),
        "trim": trim?.toJson(),
        "validate": validate?.toJson(),
      };
}

class Edit {
  String? uri;
  List<String>? options;
  List<dynamic>? blockedFields;

  Edit({
    this.uri,
    this.options,
    this.blockedFields,
  });

  factory Edit.fromJson(Map<String, dynamic> json) => Edit(
        uri: json["uri"],
        options: json["options"] == null
            ? []
            : List<String>.from(json["options"]!.map((x) => x)),
        blockedFields: json["blocked_fields"] == null
            ? []
            : List<dynamic>.from(json["blocked_fields"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "uri": uri,
        "options":
            options == null ? [] : List<dynamic>.from(options!.map((x) => x)),
        "blocked_fields": blockedFields == null
            ? []
            : List<dynamic>.from(blockedFields!.map((x) => x)),
      };
}

class EditContentRating {
  String? uri;
  List<String>? options;
  List<String>? contentRating;

  EditContentRating({
    this.uri,
    this.options,
    this.contentRating,
  });

  factory EditContentRating.fromJson(Map<String, dynamic> json) =>
      EditContentRating(
        uri: json["uri"],
        options: json["options"] == null
            ? []
            : List<String>.from(json["options"]!.map((x) => x)),
        contentRating: json["content_rating"] == null
            ? []
            : List<String>.from(json["content_rating"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "uri": uri,
        "options":
            options == null ? [] : List<dynamic>.from(options!.map((x) => x)),
        "content_rating": contentRating == null
            ? []
            : List<dynamic>.from(contentRating!.map((x) => x)),
      };
}

class EditPrivacy {
  String? uri;
  List<String>? options;
  String? contentType;
  List<Property>? properties;

  EditPrivacy({
    this.uri,
    this.options,
    this.contentType,
    this.properties,
  });

  factory EditPrivacy.fromJson(Map<String, dynamic> json) => EditPrivacy(
        uri: json["uri"],
        options: json["options"] == null
            ? []
            : List<String>.from(json["options"]!.map((x) => x)),
        contentType: json["content_type"],
        properties: json["properties"] == null
            ? []
            : List<Property>.from(
                json["properties"]!.map((x) => Property.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "uri": uri,
        "options":
            options == null ? [] : List<dynamic>.from(options!.map((x) => x)),
        "content_type": contentType,
        "properties": properties == null
            ? []
            : List<dynamic>.from(properties!.map((x) => x.toJson())),
      };
}

class Property {
  String? name;
  bool? required;
  List<String>? options;

  Property({
    this.name,
    this.required,
    this.options,
  });

  factory Property.fromJson(Map<String, dynamic> json) => Property(
        name: json["name"],
        required: json["required"],
        options: json["options"] == null
            ? []
            : List<String>.from(json["options"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "required": required,
        "options":
            options == null ? [] : List<dynamic>.from(options!.map((x) => x)),
      };
}

class Report {
  String? uri;
  List<String>? options;
  List<String>? reason;

  Report({
    this.uri,
    this.options,
    this.reason,
  });

  factory Report.fromJson(Map<String, dynamic> json) => Report(
        uri: json["uri"],
        options: json["options"] == null
            ? []
            : List<String>.from(json["options"]!.map((x) => x)),
        reason: json["reason"] == null
            ? []
            : List<String>.from(json["reason"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "uri": uri,
        "options":
            options == null ? [] : List<dynamic>.from(options!.map((x) => x)),
        "reason":
            reason == null ? [] : List<dynamic>.from(reason!.map((x) => x)),
      };
}

class Watchlater {
  String? uri;
  List<String>? options;
  bool? added;
  dynamic addedTime;

  Watchlater({
    this.uri,
    this.options,
    this.added,
    this.addedTime,
  });

  factory Watchlater.fromJson(Map<String, dynamic> json) => Watchlater(
        uri: json["uri"],
        options: json["options"] == null
            ? []
            : List<String>.from(json["options"]!.map((x) => x)),
        added: json["added"],
        addedTime: json["added_time"],
      );

  Map<String, dynamic> toJson() => {
        "uri": uri,
        "options":
            options == null ? [] : List<dynamic>.from(options!.map((x) => x)),
        "added": added,
        "added_time": addedTime,
      };
}

class Pictures {
  String? uri;
  bool? active;
  String? type;
  String? baseLink;
  List<Size>? sizes;
  String? resourceKey;
  bool? defaultPicture;

  Pictures({
    this.uri,
    this.active,
    this.type,
    this.baseLink,
    this.sizes,
    this.resourceKey,
    this.defaultPicture,
  });

  factory Pictures.fromJson(Map<String, dynamic> json) => Pictures(
        uri: json["uri"],
        active: json["active"],
        type: json["type"],
        baseLink: json["base_link"],
        sizes: json["sizes"] == null
            ? []
            : List<Size>.from(json["sizes"]!.map((x) => Size.fromJson(x))),
        resourceKey: json["resource_key"],
        defaultPicture: json["default_picture"],
      );

  Map<String, dynamic> toJson() => {
        "uri": uri,
        "active": active,
        "type": type,
        "base_link": baseLink,
        "sizes": sizes == null
            ? []
            : List<dynamic>.from(sizes!.map((x) => x.toJson())),
        "resource_key": resourceKey,
        "default_picture": defaultPicture,
      };
}

class Size {
  int? width;
  int? height;
  String? link;
  String? linkWithPlayButton;

  Size({
    this.width,
    this.height,
    this.link,
    this.linkWithPlayButton,
  });

  factory Size.fromJson(Map<String, dynamic> json) => Size(
        width: json["width"],
        height: json["height"],
        link: json["link"],
        linkWithPlayButton: json["link_with_play_button"],
      );

  Map<String, dynamic> toJson() => {
        "width": width,
        "height": height,
        "link": link,
        "link_with_play_button": linkWithPlayButton,
      };
}

class Play {
  List<Download>? progressive;
  Dash? hls;
  Dash? dash;
  String? status;

  Play({
    this.progressive,
    this.hls,
    this.dash,
    this.status,
  });

  factory Play.fromJson(Map<String, dynamic> json) => Play(
        progressive: json["progressive"] == null
            ? []
            : List<Download>.from(
                json["progressive"]!.map((x) => Download.fromJson(x))),
        hls: json["hls"] == null ? null : Dash.fromJson(json["hls"]),
        dash: json["dash"] == null ? null : Dash.fromJson(json["dash"]),
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "progressive": progressive == null
            ? []
            : List<dynamic>.from(progressive!.map((x) => x.toJson())),
        "hls": hls?.toJson(),
        "dash": dash?.toJson(),
        "status": status,
      };
}

class Dash {
  DateTime? linkExpirationTime;
  String? link;

  Dash({
    this.linkExpirationTime,
    this.link,
  });

  factory Dash.fromJson(Map<String, dynamic> json) => Dash(
        linkExpirationTime: json["link_expiration_time"] == null
            ? null
            : DateTime.parse(json["link_expiration_time"]),
        link: json["link"],
      );

  Map<String, dynamic> toJson() => {
        "link_expiration_time": linkExpirationTime?.toIso8601String(),
        "link": link,
      };
}

class Privacy {
  String? view;
  String? embed;
  bool? download;
  bool? add;
  String? comments;
  bool? allowShareLink;

  Privacy({
    this.view,
    this.embed,
    this.download,
    this.add,
    this.comments,
    this.allowShareLink,
  });

  factory Privacy.fromJson(Map<String, dynamic> json) => Privacy(
        view: json["view"],
        embed: json["embed"],
        download: json["download"],
        add: json["add"],
        comments: json["comments"],
        allowShareLink: json["allow_share_link"],
      );

  Map<String, dynamic> toJson() => {
        "view": view,
        "embed": embed,
        "download": download,
        "add": add,
        "comments": comments,
        "allow_share_link": allowShareLink,
      };
}

class ReviewPage {
  bool? active;
  String? link;
  bool? isShareable;

  ReviewPage({
    this.active,
    this.link,
    this.isShareable,
  });

  factory ReviewPage.fromJson(Map<String, dynamic> json) => ReviewPage(
        active: json["active"],
        link: json["link"],
        isShareable: json["is_shareable"],
      );

  Map<String, dynamic> toJson() => {
        "active": active,
        "link": link,
        "is_shareable": isShareable,
      };
}

class Stats {
  int? plays;

  Stats({
    this.plays,
  });

  factory Stats.fromJson(Map<String, dynamic> json) => Stats(
        plays: json["plays"],
      );

  Map<String, dynamic> toJson() => {
        "plays": plays,
      };
}

class Transcode {
  String? status;

  Transcode({
    this.status,
  });

  factory Transcode.fromJson(Map<String, dynamic> json) => Transcode(
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
      };
}

class Upload {
  String? status;
  dynamic link;
  dynamic uploadLink;
  dynamic completeUri;
  dynamic form;
  dynamic approach;
  dynamic size;
  dynamic redirectUrl;

  Upload({
    this.status,
    this.link,
    this.uploadLink,
    this.completeUri,
    this.form,
    this.approach,
    this.size,
    this.redirectUrl,
  });

  factory Upload.fromJson(Map<String, dynamic> json) => Upload(
        status: json["status"],
        link: json["link"],
        uploadLink: json["upload_link"],
        completeUri: json["complete_uri"],
        form: json["form"],
        approach: json["approach"],
        size: json["size"],
        redirectUrl: json["redirect_url"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "link": link,
        "upload_link": uploadLink,
        "complete_uri": completeUri,
        "form": form,
        "approach": approach,
        "size": size,
        "redirect_url": redirectUrl,
      };
}

class Uploader {
  Pictures? pictures;

  Uploader({
    this.pictures,
  });

  factory Uploader.fromJson(Map<String, dynamic> json) => Uploader(
        pictures: json["pictures"] == null
            ? null
            : Pictures.fromJson(json["pictures"]),
      );

  Map<String, dynamic> toJson() => {
        "pictures": pictures?.toJson(),
      };
}

class User {
  String? uri;
  String? name;
  String? link;
  Capabilities? capabilities;
  String? location;
  String? gender;
  dynamic bio;
  dynamic shortBio;
  DateTime? createdTime;
  Pictures? pictures;
  List<dynamic>? websites;
  UserMetadata? metadata;
  LocationDetails? locationDetails;
  List<dynamic>? skills;
  bool? availableForHire;
  bool? canWorkRemotely;
  Preferences? preferences;
  List<String>? contentFilter;
  String? resourceKey;
  String? account;

  User({
    this.uri,
    this.name,
    this.link,
    this.capabilities,
    this.location,
    this.gender,
    this.bio,
    this.shortBio,
    this.createdTime,
    this.pictures,
    this.websites,
    this.metadata,
    this.locationDetails,
    this.skills,
    this.availableForHire,
    this.canWorkRemotely,
    this.preferences,
    this.contentFilter,
    this.resourceKey,
    this.account,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        uri: json["uri"],
        name: json["name"],
        link: json["link"],
        capabilities: json["capabilities"] == null
            ? null
            : Capabilities.fromJson(json["capabilities"]),
        location: json["location"],
        gender: json["gender"],
        bio: json["bio"],
        shortBio: json["short_bio"],
        createdTime: json["created_time"] == null
            ? null
            : DateTime.parse(json["created_time"]),
        pictures: json["pictures"] == null
            ? null
            : Pictures.fromJson(json["pictures"]),
        websites: json["websites"] == null
            ? []
            : List<dynamic>.from(json["websites"]!.map((x) => x)),
        metadata: json["metadata"] == null
            ? null
            : UserMetadata.fromJson(json["metadata"]),
        locationDetails: json["location_details"] == null
            ? null
            : LocationDetails.fromJson(json["location_details"]),
        skills: json["skills"] == null
            ? []
            : List<dynamic>.from(json["skills"]!.map((x) => x)),
        availableForHire: json["available_for_hire"],
        canWorkRemotely: json["can_work_remotely"],
        preferences: json["preferences"] == null
            ? null
            : Preferences.fromJson(json["preferences"]),
        contentFilter: json["content_filter"] == null
            ? []
            : List<String>.from(json["content_filter"]!.map((x) => x)),
        resourceKey: json["resource_key"],
        account: json["account"],
      );

  Map<String, dynamic> toJson() => {
        "uri": uri,
        "name": name,
        "link": link,
        "capabilities": capabilities?.toJson(),
        "location": location,
        "gender": gender,
        "bio": bio,
        "short_bio": shortBio,
        "created_time": createdTime?.toIso8601String(),
        "pictures": pictures?.toJson(),
        "websites":
            websites == null ? [] : List<dynamic>.from(websites!.map((x) => x)),
        "metadata": metadata?.toJson(),
        "location_details": locationDetails?.toJson(),
        "skills":
            skills == null ? [] : List<dynamic>.from(skills!.map((x) => x)),
        "available_for_hire": availableForHire,
        "can_work_remotely": canWorkRemotely,
        "preferences": preferences?.toJson(),
        "content_filter": contentFilter == null
            ? []
            : List<dynamic>.from(contentFilter!.map((x) => x)),
        "resource_key": resourceKey,
        "account": account,
      };
}

class Capabilities {
  bool? hasLiveSubscription;
  bool? hasEnterpriseLihp;
  bool? hasSvvTimecodedComments;
  bool? hasSimplifiedEnterpriseAccount;

  Capabilities({
    this.hasLiveSubscription,
    this.hasEnterpriseLihp,
    this.hasSvvTimecodedComments,
    this.hasSimplifiedEnterpriseAccount,
  });

  factory Capabilities.fromJson(Map<String, dynamic> json) => Capabilities(
        hasLiveSubscription: json["hasLiveSubscription"],
        hasEnterpriseLihp: json["hasEnterpriseLihp"],
        hasSvvTimecodedComments: json["hasSvvTimecodedComments"],
        hasSimplifiedEnterpriseAccount: json["hasSimplifiedEnterpriseAccount"],
      );

  Map<String, dynamic> toJson() => {
        "hasLiveSubscription": hasLiveSubscription,
        "hasEnterpriseLihp": hasEnterpriseLihp,
        "hasSvvTimecodedComments": hasSvvTimecodedComments,
        "hasSimplifiedEnterpriseAccount": hasSimplifiedEnterpriseAccount,
      };
}

class LocationDetails {
  String? formattedAddress;
  dynamic latitude;
  dynamic longitude;
  dynamic city;
  dynamic state;
  dynamic neighborhood;
  dynamic subLocality;
  dynamic stateIsoCode;
  dynamic country;
  dynamic countryIsoCode;

  LocationDetails({
    this.formattedAddress,
    this.latitude,
    this.longitude,
    this.city,
    this.state,
    this.neighborhood,
    this.subLocality,
    this.stateIsoCode,
    this.country,
    this.countryIsoCode,
  });

  factory LocationDetails.fromJson(Map<String, dynamic> json) =>
      LocationDetails(
        formattedAddress: json["formatted_address"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        city: json["city"],
        state: json["state"],
        neighborhood: json["neighborhood"],
        subLocality: json["sub_locality"],
        stateIsoCode: json["state_iso_code"],
        country: json["country"],
        countryIsoCode: json["country_iso_code"],
      );

  Map<String, dynamic> toJson() => {
        "formatted_address": formattedAddress,
        "latitude": latitude,
        "longitude": longitude,
        "city": city,
        "state": state,
        "neighborhood": neighborhood,
        "sub_locality": subLocality,
        "state_iso_code": stateIsoCode,
        "country": country,
        "country_iso_code": countryIsoCode,
      };
}

class UserMetadata {
  FluffyConnections? connections;

  UserMetadata({
    this.connections,
  });

  factory UserMetadata.fromJson(Map<String, dynamic> json) => UserMetadata(
        connections: json["connections"] == null
            ? null
            : FluffyConnections.fromJson(json["connections"]),
      );

  Map<String, dynamic> toJson() => {
        "connections": connections?.toJson(),
      };
}

class FluffyConnections {
  Albums? albums;
  Albums? appearances;
  Albums? categories;
  Albums? channels;
  Recommendations? feed;
  Albums? followers;
  Albums? following;
  Albums? groups;
  Albums? likes;
  Recommendations? membership;
  Albums? moderatedChannels;
  Albums? portfolios;
  Albums? videos;
  Albums? watchlater;
  Albums? shared;
  Albums? pictures;
  Albums? watchedVideos;
  Recommendations? foldersRoot;
  Albums? folders;
  Albums? teams;
  Albums? permissionPolicies;
  Albums? block;

  FluffyConnections({
    this.albums,
    this.appearances,
    this.categories,
    this.channels,
    this.feed,
    this.followers,
    this.following,
    this.groups,
    this.likes,
    this.membership,
    this.moderatedChannels,
    this.portfolios,
    this.videos,
    this.watchlater,
    this.shared,
    this.pictures,
    this.watchedVideos,
    this.foldersRoot,
    this.folders,
    this.teams,
    this.permissionPolicies,
    this.block,
  });

  factory FluffyConnections.fromJson(Map<String, dynamic> json) =>
      FluffyConnections(
        albums: json["albums"] == null ? null : Albums.fromJson(json["albums"]),
        appearances: json["appearances"] == null
            ? null
            : Albums.fromJson(json["appearances"]),
        categories: json["categories"] == null
            ? null
            : Albums.fromJson(json["categories"]),
        channels:
            json["channels"] == null ? null : Albums.fromJson(json["channels"]),
        feed: json["feed"] == null
            ? null
            : Recommendations.fromJson(json["feed"]),
        followers: json["followers"] == null
            ? null
            : Albums.fromJson(json["followers"]),
        following: json["following"] == null
            ? null
            : Albums.fromJson(json["following"]),
        groups: json["groups"] == null ? null : Albums.fromJson(json["groups"]),
        likes: json["likes"] == null ? null : Albums.fromJson(json["likes"]),
        membership: json["membership"] == null
            ? null
            : Recommendations.fromJson(json["membership"]),
        moderatedChannels: json["moderated_channels"] == null
            ? null
            : Albums.fromJson(json["moderated_channels"]),
        portfolios: json["portfolios"] == null
            ? null
            : Albums.fromJson(json["portfolios"]),
        videos: json["videos"] == null ? null : Albums.fromJson(json["videos"]),
        watchlater: json["watchlater"] == null
            ? null
            : Albums.fromJson(json["watchlater"]),
        shared: json["shared"] == null ? null : Albums.fromJson(json["shared"]),
        pictures:
            json["pictures"] == null ? null : Albums.fromJson(json["pictures"]),
        watchedVideos: json["watched_videos"] == null
            ? null
            : Albums.fromJson(json["watched_videos"]),
        foldersRoot: json["folders_root"] == null
            ? null
            : Recommendations.fromJson(json["folders_root"]),
        folders:
            json["folders"] == null ? null : Albums.fromJson(json["folders"]),
        teams: json["teams"] == null ? null : Albums.fromJson(json["teams"]),
        permissionPolicies: json["permission_policies"] == null
            ? null
            : Albums.fromJson(json["permission_policies"]),
        block: json["block"] == null ? null : Albums.fromJson(json["block"]),
      );

  Map<String, dynamic> toJson() => {
        "albums": albums?.toJson(),
        "appearances": appearances?.toJson(),
        "categories": categories?.toJson(),
        "channels": channels?.toJson(),
        "feed": feed?.toJson(),
        "followers": followers?.toJson(),
        "following": following?.toJson(),
        "groups": groups?.toJson(),
        "likes": likes?.toJson(),
        "membership": membership?.toJson(),
        "moderated_channels": moderatedChannels?.toJson(),
        "portfolios": portfolios?.toJson(),
        "videos": videos?.toJson(),
        "watchlater": watchlater?.toJson(),
        "shared": shared?.toJson(),
        "pictures": pictures?.toJson(),
        "watched_videos": watchedVideos?.toJson(),
        "folders_root": foldersRoot?.toJson(),
        "folders": folders?.toJson(),
        "teams": teams?.toJson(),
        "permission_policies": permissionPolicies?.toJson(),
        "block": block?.toJson(),
      };
}

class Preferences {
  Videos? videos;
  List<dynamic>? webinarRegistrantLowerWatermarkBannerDismissed;

  Preferences({
    this.videos,
    this.webinarRegistrantLowerWatermarkBannerDismissed,
  });

  factory Preferences.fromJson(Map<String, dynamic> json) => Preferences(
        videos: json["videos"] == null ? null : Videos.fromJson(json["videos"]),
        webinarRegistrantLowerWatermarkBannerDismissed:
            json["webinar_registrant_lower_watermark_banner_dismissed"] == null
                ? []
                : List<dynamic>.from(
                    json["webinar_registrant_lower_watermark_banner_dismissed"]!
                        .map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "videos": videos?.toJson(),
        "webinar_registrant_lower_watermark_banner_dismissed":
            webinarRegistrantLowerWatermarkBannerDismissed == null
                ? []
                : List<dynamic>.from(
                    webinarRegistrantLowerWatermarkBannerDismissed!
                        .map((x) => x)),
      };
}

class Videos {
  List<String>? rating;
  Privacy? privacy;

  Videos({
    this.rating,
    this.privacy,
  });

  factory Videos.fromJson(Map<String, dynamic> json) => Videos(
        rating: json["rating"] == null
            ? []
            : List<String>.from(json["rating"]!.map((x) => x)),
        privacy:
            json["privacy"] == null ? null : Privacy.fromJson(json["privacy"]),
      );

  Map<String, dynamic> toJson() => {
        "rating":
            rating == null ? [] : List<dynamic>.from(rating!.map((x) => x)),
        "privacy": privacy?.toJson(),
      };
}
