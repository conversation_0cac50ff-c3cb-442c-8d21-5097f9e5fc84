import 'dart:async';
import 'dart:math';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:legacy/utils/app_pref.dart';

import '../main.dart';

class FirebaseNotification {
  fcm() async {
    final fcmToken = await FirebaseMessaging.instance.getToken();
    print('111222-----$fcmToken,,,');
    AppPref.setString('fcmToken', fcmToken.toString() ?? 'k');

    return fcmToken;
  }

  permissions() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('User granted permission');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      print('User granted provisional permission');
    } else {
      print('User declined or has not accepted permission');
    }
  }

  foregroundnotification(context) {
    print('hii');
    FirebaseMessaging.onMessage.listen(
      (RemoteMessage message) {
        debugPrint("onMessage:");
        print("onMessage: $message");
        AwesomeNotifications().createNotification(
          content: NotificationContent(
            id: Random().nextInt(1000),
            channelKey: 'church',
            title: message.data['title'],
            body: message.data['message'],
            actionType: ActionType.Default,
            summary: message.data['type'],
          ),
        );
        print('2222222222222222----${message.data.toString()}');
// );
//         final snackBar =
//             SnackBar(content: Text(message.notification?.title ?? ""));
//         ScaffoldMessenger.of(context).showSnackBar(snackBar);
      },
    );
  }
}

// class NotificationControllerFg {
//   /// Use this method to detect when the user taps on a notification or action button
//   @pragma("vm:entry-point")
//   static Future<void> onActionReceivedMethod(
//       ReceivedAction receivedAction) async {
//         pageload=true;
//         FgNotificationReceived.value=true;

//         // bgNotificationReceived.value=true;
//     // Your code goes here
//     print('hii1212---------');
//       print('${pageload}---------------111111111222222222');

//     if (receivedAction.summary == 'notification') {
//       navigatorKey.currentState?.pushNamedAndRemoveUntil(
//           '/notification', (route) => route.settings.name != '/notification');
//     } else if (receivedAction.summary == 'message') {
//       navigatorKey.currentState?.pushNamedAndRemoveUntil(
//           '/msgboard', (route) => route.settings.name != '/msgboard');
//     } else if (receivedAction.summary == 'article') {
//       navigatorKey.currentState?.pushNamedAndRemoveUntil(
//           '/article', (route) => route.settings.name != '/article');
//     } else if (receivedAction.summary == 'event') {
//       navigatorKey.currentState?.pushNamedAndRemoveUntil(
//           '/event', (route) => route.settings.name != '/event');
//     } else if (receivedAction.summary == 'audio') {
//       navigatorKey.currentState?.pushNamedAndRemoveUntil(
//           '/podcast', (route) => route.settings.name != '/podcast');
//     } else if (receivedAction.summary == 'video') {
//       navigatorKey.currentState?.pushNamedAndRemoveUntil(
//           '/channels', (route) => route.settings.name != '/channels');
//     }

//     // Navigate into pages, avoiding to open the notification details page over another details page already opened
//     print('-------$receivedAction');
//     // navigatorKey.currentState?.pushNamedAndRemoveUntil(
//     //   '/second',
//     //   (route) => (route.settings.name != '/second') || route.isFirst,
//     // );
//   }
// }
class NotificationController {
  static final StreamController<String> _bgStreamController =
      StreamController<String>.broadcast();

  static Stream<String> get bgStream => _bgStreamController.stream;

  /// Use this method to detect when the user taps on a notification or action button
  @pragma("vm:entry-point")
  static Future<void> onActionReceivedMethod(
      ReceivedAction receivedAction) async {
    if (receivedAction.actionLifeCycle == NotificationLifeCycle.Foreground) {
      if (receivedAction.summary == 'notification') {
        controller.jumpToTab(3);
      } else if (receivedAction.summary == 'message') {
        navigatorKey.currentState?.pushNamed('/msgboard');
      } else if (receivedAction.summary == 'article') {
        navigatorKey.currentState?.pushNamed('/article');
      } else if (receivedAction.summary == 'event') {
        navigatorKey.currentState?.pushNamed('/event');
      } else if (receivedAction.summary == 'audio') {
        controller.jumpToTab(2);
      } else if (receivedAction.summary == 'video') {
        controller.jumpToTab(1);
      }
    } else if (receivedAction.actionLifeCycle ==
            NotificationLifeCycle.Background ||
        receivedAction.actionLifeCycle == NotificationLifeCycle.AppKilled ||
        receivedAction.actionLifeCycle == NotificationLifeCycle.Terminated) {
      // _bgStreamController.sink.add("/notification");
      // pageMove.value = "/notification";
      if (receivedAction.summary == 'notification') {
        _bgStreamController.sink.add("/notification");
        pageMove.value = "/notification";
      } else if (receivedAction.summary == 'message') {
        _bgStreamController.sink.add("/msgboard");
        pageMove.value = "/msgboard";
      } else if (receivedAction.summary == 'article') {
        _bgStreamController.sink.add("/article");
        pageMove.value = "/article";
      } else if (receivedAction.summary == 'event') {
        _bgStreamController.sink.add("/event");
        pageMove.value = "/event";
      } else if (receivedAction.summary == 'audio') {
        _bgStreamController.sink.add("/podcast");
        pageMove.value = "/podcast";
      } else if (receivedAction.summary == 'video') {
        _bgStreamController.sink.add("/channels");
        pageMove.value = "/channels";
      }
    }
    print('-------$receivedAction');
  }
}
