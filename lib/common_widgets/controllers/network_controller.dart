import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter/material.dart';
import 'package:legacy/common_widgets/tools.dart';
import 'package:legacy/common_widgets/videoplayer/models/videoplayermodels.dart';
import 'package:legacy/features/announcements/models/announcementmodel.dart';
import 'package:legacy/features/articles/models/articledetailmodel.dart';
import 'package:legacy/features/articles/models/articlesmodel.dart';
import 'package:legacy/features/channels/models/channelsmain.dart';
import 'package:legacy/features/events/models/event_item_model.dart';
import 'package:legacy/features/events/models/events_response_all.dart';
import 'package:legacy/features/homepage/model/recent_videomodel.dart';
import 'package:legacy/features/loginpage/models/loginmodel.dart';
import 'package:legacy/features/message/models/messagedetailmodel.dart';
import 'package:legacy/features/podcasts/models/podcastlistmodel.dart';
import 'package:legacy/features/podcasts/models/podcastmodel.dart';
import 'package:legacy/utils/app_pref.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

import '../../features/announcements/models/notificationdetailmodel.dart';
import '../../features/channels/models/channellistmodel.dart';
import '../../features/forgetpassword/model/forgetpwd_model.dart';
import '../../features/message/models/chatmodel.dart';
import '../../features/signin/model/signinmodel.dart';
import '../../main.dart';
import '../../utils/constants.dart';
import '../../utils/models/fcmpassmodel.dart';
import '../../utils/models/logoutmodel.dart';
import '../../utils/models/withoutloginmodel.dart';
import '../../utils/print_log.dart';
import '../sidedrawer/models/sidedrawermodel.dart';

class NetworkController {
  static final NetworkController _singleton = NetworkController._internal();
  NetworkController._internal();
  Dio dio = Dio();
  factory NetworkController() {
    _singleton.dio = Dio();
    _singleton.dio.options.baseUrl = baseAppUrl;
    _singleton.dio.options.connectTimeout = Duration(seconds: 20);
    _singleton.dio.options.receiveTimeout = Duration(seconds: 20);
    _singleton.dio.interceptors.add(
      RetryInterceptor(
        dio: _singleton.dio,
        logPrint: print,
        retries: 2,
        retryDelays: const [
          Duration(seconds: 2),
          Duration(seconds: 2),
        ],
        retryableExtraStatuses: {400},
      ),
    );
    // Dio dio = Dio();
    _singleton.dio.interceptors.add(PrettyDioLogger(
      requestBody: true,
      requestHeader: true,
    ));
// customization
    _singleton.dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          print('-------1');

          // options.headers['Authorization'] = "Bearer " + (getAuthToken() ?? "");
          // try {
          //   options.headers['x-userpool-id'] =
          //       organisationData.userPoolId;
          // } catch (e) {
          //   print(e);
          // }
          // options.headers['X-NAME-NO'] = 'vtwo_dev_admin';
          // log(options.uri.toString());
          // log(jsonEncode(options.headers));
          return handler.next(options);
        },
        onResponse: (response, handler) {
          print('-------2');
          print(jsonEncode(response.data));
          return handler.next(response);
        },
        onError: (DioError e, handler) {
          print('-------3');

          print(e.toString());
          print(e.response?.statusCode.toString() ?? "11111" "ppppppppppppppp");
          if (e.response!.statusCode == 401) {
            print('1---------------------2');
            navigatorKey.currentState?.pushNamedAndRemoveUntil(
              '/login',
              (route) => (route.settings.name != '/login') || route.isFirst,
            );
          }
          if (e.type == DioErrorType.connectionError) {
            // alertmsg("Network error");
            print('error occured----1');
          }
          return handler.next(e);
        },
      ),
    );
    return _singleton;
  }

  Future<dynamic> loginAuth(String? username, String? password) async {
    var url = "$baseAppUrl/members/login";
    var params = {"username": username, "password": password};
    printLog(username);
    printLog(password);
    printLog(params.toString());
    printLog(url);
    try {
      Response response = await dio.post(
        url,
        data: jsonEncode(params),
      );
      printLog(response.toString());
      AppPref.setString('usrname', username.toString());
      AppPref.setString('pass', password.toString());
      print('1212121212121${response.data}');
      return LoginCred.fromJson(response.data);
    } on DioError catch (e) {
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> signingIn(
      String? email, String? password, String? name) async {
    var url = "$baseAppUrl/members/register";
    var params = {"name": name, "password": password, "email": email};
    try {
      Response response = await dio.post(url,
          data: jsonEncode(params),
          options: Options(
            validateStatus: (status) => true,
          ));
      print(response.data);
      return SignInModel.fromJson(response.data);
    } on DioError catch (e) {
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> forgetPwd(String? email) async {
    var url = "$baseAppUrl/members/forgot-password/$email";

    try {
      Response response = await dio.put(
        url,
      );
      print(response.data);
      print(url);
      return ForgetPsw.fromJson(response.data);
    } on DioError catch (e) {
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> recentVideoLoad(String token) async {
    var url1 = "$baseAppUrl/members/recent-videos";
    print('object111111${AppPref.getString('vimeo_id')}');
    try {
      Response response = await dio.get(url1,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return RecentModel.fromJson(response.data);
    } on DioError catch (e) {
      print(e.response);
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> channelMainVideoLoad(String token) async {
    var url1 = "$baseAppUrl/members/channel/video";
    print('object111111${AppPref.getString('vimeo_id')}');
    try {
      Response response = await dio.get(url1,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return ChannelVideoModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> podcastMainAudioLoad(String token) async {
    var url = "$baseAppUrl/members/channel/audio";
    print('object111111${AppPref.getString('vimeo_id')}');
    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return PodcastVideomodel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> channelListLoad(String token, String categoryId) async {
    var url = "$baseAppUrl/members/list-video/$categoryId";

    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return ChannelListVideoModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> notificationLoad(String token) async {
    var url = "$baseAppUrl/members/notifications";

    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return NotificationModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> podcastListLoad(String token, String categoryId) async {
    var url = "$baseAppUrl/members/list-audio/$categoryId";

    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return PodcastListModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> getAllEvents(String token) async {
    var url = "$baseAppUrl/members/events";

    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return EventsResponseAll.fromJson(response.data);
    } on DioError catch (e) {
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> getSelectedDaysEvents(
      String token, String date, String offset) async {
    var url = "$baseAppUrl/members/events/$date/list?timeZone=$offset";

    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return EventItemModel.fromJson(response.data);
    } on DioError catch (e) {
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> getvideo(String token, String vimeoId) async {
    var url = "https://api.vimeo.com/videos/$vimeoId";
    print('*********-----$token');

    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return VideoPlayerModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> getarticles(String token) async {
    var url = "$baseAppUrl/members/article-list";
    print('*********-----$token');

    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return ArticlesModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> getannouncementdetail(
      String token, String notificationId) async {
    var url = "$baseAppUrl/members/notification/$notificationId";
    print('*********-----$token');

    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return NotificationDetailModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> articledetails(String token, String articleId) async {
    var url = "$baseAppUrl/members/article/$articleId";
    print('*********-----$token');

    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return ArticlesDetailModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> articleMarkAsRead(String token, String articleId) async {
    var url = "$baseAppUrl/members/article/$articleId";
    print('*********-----$token');
    var param = {
      "markAsRead": "true",
    };

    try {
      Response response = await dio.put(url,
          data: jsonEncode(param),
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      // return ArticlesDetailModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> notificationMarkAsRead(
      String token, String notificationId) async {
    var url = "$baseAppUrl/members/notification/$notificationId";
    print('*********-----$token');
    var param = {
      "markAsRead": "true",
    };

    try {
      Response response = await dio.put(url,
          data: jsonEncode(param),
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('--noti   mark as---${response.data}');
      // return ArticlesDetailModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> chatscreen(String token) async {
    var url = "$baseAppUrl/members/messages";
    print('*********-----$token');

    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return ChatModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> chatdetailscreen(
    String token,
    String messageId,
  ) async {
    var url = "$baseAppUrl/members/messages/$messageId/comments";
    print('*********-----$token');

    try {
      Response response = await dio.get(url,
          options: Options(validateStatus: (status) => true, headers: {
            'Authorization': 'Bearer $token',
          }));
      print('-----${response.data}');
      return ChatDetailModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> sendmsg(String token, String messageId, params) async {
    var url = "$baseAppUrl/members/messages/$messageId/comments";
    print('*********-----$token');

    try {
      Response response = await dio.post(url,
          data: jsonEncode(params),
          options: Options(validateStatus: (status) => true, headers: {
            'Authorization': 'Bearer $token',
          }));
      print('-----${response.data}');
      return ChatDetailModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> sidedrawer(String token) async {
    var url = "$baseAppUrl/members/unread-count";

    print('*********-----$token');

    try {
      Response response = await dio.get(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return SideDrawerModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> loginApp(
      String deviceId, String userId, String fcmToken) async {
    var url = "$baseAppUrl/members/fcm";
    String? token = AppPref.getString('token');

    print('*********-----$token');
    var params = {
      "deviceName": "devname",
      "deviceType": "android",
      "deviceId": deviceId,
      "osVersion": "mobileOSVersion",
      "userId": userId,
      "fcmToken": fcmToken
    };
    print("PARAMS===$params");
    try {
      Response response = await dio.post(url,
          data: jsonEncode(params),
          options: Options(validateStatus: (status) => true, headers: {
            'Authorization': 'Bearer $token',
          }));
      print('-----5555555555555${response.data}');
      return FcmPass.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> logoutApp() async {
    String? token = AppPref.getString('token');
    String? deviceId = AppPref.getString('deviceId');
    var url = "$baseAppUrl/members/logout/$deviceId";

    print('*********-----$token');

    try {
      Response response = await dio.delete(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      if (response.data['status'] == true) {
        return 'Logout succesfull';
      } else {
        return 'Error occured';
      }
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> withoutLoggin() async {
    String? deviceId = AppPref.getString('deviceId');
    var url = "$baseAppUrl/members/device-id";

    var params = {"deviceId": "$deviceId"};

    try {
      Response response = await dio.post(url,
          data: jsonEncode(params),
          options: Options(
              // validateStatus: (status) => true,
              ));
      var k = WithoutLoginModel.fromJson(response.data);
      print('-----${response.data}');
      print("${response.data['status']}------------------78787");
      if (response.data['status'] == true) {
        AppPref.setString('statuslogin', k.status.toString());

        // AppPref.setString('LOGIN_DETAILS',k? '' );
        AppPref.setString('token', k.data?.token ?? '***********+64');
        AppPref.setString('ref_token', k.data?.refreshToken ?? '');
        AppPref.setString('usr_id', k.data?.userId ?? '');
        AppPref.setString('vimeo_id', k.data?.vimeoId ?? '');
      }
      return WithoutLoginModel.fromJson(response.data);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }

  Future<dynamic> deleteAccount() async {
    String? token = AppPref.getString('token');
    String? deviceId = AppPref.getString('deviceId');
    var url = "$baseAppUrl/members/delete-profile";

    print('*********-----$token');

    try {
      Response response = await dio.delete(url,
          options: Options(
              // validateStatus: (status) => true,
              headers: {
                'Authorization': 'Bearer $token',
              }));
      print('-----${response.data}');
      return (response.data['message']);
    } on DioError catch (e) {
      print('2222222${e.toString()}');
      throw Exception(e.response?.data['message'] ?? "Error occurred");
    }
  }
}
