import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:legacy/utils/app_pref.dart';
import 'package:platform_device_id/platform_device_id.dart';

final _globalKey = GlobalKey<ScaffoldMessengerState>();

alertmsg(String? message, BuildContext context) {
  // Flushbar(
  //   message: message,
  //   margin: const EdgeInsets.all(8),
  //   borderRadius: BorderRadius.circular(8),
  //   duration: Duration(seconds: 1),
  // );
  var snackBar = SnackBar(
    content: Text(
      message.toString(),
    ),
    shape: const StadiumBorder(),
    behavior: SnackBarBehavior.floating,
    width: 400,
  );
  ScaffoldMessenger.of(context).showSnackBar(snackBar);
  // _globalKey.currentState?.showSnackBar(snackBar);
}

dev_id() async {
  String? deviceId = await PlatformDeviceId.getDeviceId;
  print('2.2.2.2$deviceId');
  AppPref.setString('deviceId', deviceId.toString());
}
// final box = AppPref;

// writeToStorage(String key, String value) {
//   AppPref.setString(key, value);
// }
//  readFromStorage(String key) {
//   AppPref.getString(key);
// }
// getCustomerId() {
//   return readFromStorage(customerId);
// }

// getCustomerRole() {
//   return readFromStorage(customerRole);
// }

// getUserName() {
//   return readFromStorage('username');
// }

// getAuthToken() {
//   return readFromStorage(cognitoToken);
// }

// clearAuthToken() {
//   GetStorage().remove(cognitoToken);
// }
getUser() {
  return AppPref.getString('usrname');
}

getPass() {
  return AppPref.getString('pass');
}

bool isLoggedIn() {
  var isLogged = AppPref.getBool('loggedIn');
  if (isLogged == null) {
    return false;
  }
  return isLogged == "TRUE";
}
