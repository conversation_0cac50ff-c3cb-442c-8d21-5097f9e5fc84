import 'package:flutter/material.dart';

import '../utils/appcolors.dart';

class AppFormField extends StatelessWidget {
  final String hint;
  final TextEditingController controller;
  final String label;
  final String icon;
  final IconButton icon_suffix;
  final bool obs;
  String? Function(String?)? val;

  AppFormField({
    super.key,
    required this.label,
    required this.controller,
    required this.hint,
    required this.icon,
    required this.icon_suffix,
    required this.obs,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      obscureText: obs,
      validator: val,
      autofocus: false,
      controller: controller,
      decoration: InputDecoration(
          focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.colorPrimary),
              borderRadius: BorderRadius.circular(16)),
          prefixIcon: Padding(
            padding: const EdgeInsets.all(15.0),
            child: Image.asset(
              icon,
              height: 20,
              width: 10,
            ),
          ),
          suffixIcon: icon_suffix,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(16)),
          labelText: label,
          labelStyle: TextStyle(color: AppColors.colorPrimary),
          hintText: hint),
    );
  }
}
